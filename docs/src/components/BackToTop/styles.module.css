.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: var(--ifm-color-primary);
  color: var(--back-to-top-text-color);
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 100;
  box-shadow: 0 2px 8px var(--back-to-top-shadow-color);
}

.back-to-top:hover {
  background: var(--ifm-color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--back-to-top-shadow-color-hover);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

@media screen and (max-width: 768px) {
  .back-to-top {
    bottom: 1.5rem;
    right: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
  }
}
