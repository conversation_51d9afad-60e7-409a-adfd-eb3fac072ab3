/**
 * Feature cards styles
 * Homepage feature section with interactive cards and animations
 */

.features-section {
  display: flex;
  align-items: center;
  padding: 6rem 0;
  width: 100%;
  background: var(--feature-bg);
  position: relative;
  overflow: hidden;
  margin: 0;
}

.features-section__border {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--ifm-color-primary-lighter), transparent);
}

.features-section > div {
  display: grid;
  gap: 2rem;
}

.feature-card {
  height: 100%;
  padding: 2.5rem;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 16px;
  box-shadow: 0 8px 32px var(--card-shadow);
  backdrop-filter: blur(10px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
  cursor: pointer;
}

.feature-card__shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    var(--shine-color),
    transparent
  );
  transition: 0.5s;
  pointer-events: none;
  z-index: 1;
}

.feature-card:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 0 12px 40px var(--hover-shadow);
  border-color: var(--hover-border);
  background: var(--hover-bg);
}

.feature-card:hover .feature-card__shine {
  left: 100%;
}

.feature-card:active {
  transform: translateY(-1px) scale(1.005);
  transition: 0.1s;
}

.feature-card__icon {
  width: 48px;
  height: 48px;
  margin-bottom: 1.5rem;
  color: var(--ifm-color-primary);
  transition: all 0.4s ease;
  filter: drop-shadow(0 0 8px var(--card-shadow));
}

.feature-card:hover .feature-card__icon {
  transform: scale(1.05);
}

.feature-card__title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--title-color);
  transition: all 0.3s ease;
  text-shadow: 0 0 20px var(--card-shadow);
}

.feature-card:hover .feature-card__title {
  background: linear-gradient(120deg, var(--ifm-color-primary-light), var(--ifm-color-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.feature-card__description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--desc-color);
  margin: 0;
  transition: color 0.3s ease;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation delay for each feature card */
.features-section > div > div:nth-child(1) .feature-card {
  animation-delay: 0.2s;
}

.features-section > div > div:nth-child(2) .feature-card {
  animation-delay: 0.4s;
}

.features-section > div > div:nth-child(3) .feature-card {
  animation-delay: 0.6s;
}

/* Responsive design */
@media screen and (max-width: 996px) {
  .features-section {
    padding: 4rem 1.5rem;
  }
  
  .features-section > div {
    gap: 1.5rem;
  }

  .feature-section__column {
    margin: 1rem;
  }

  .feature-card {
    padding: 2rem;
  }
  
  .feature-card__icon {
    width: 40px;
    height: 40px;
    margin-bottom: 1rem;
  }
}

@media screen and (max-width: 768px) {
  .features-section {
    padding: 2rem 1rem;
  }
  
  .feature-card__title {
    font-size: 1.25rem;
  }
}
