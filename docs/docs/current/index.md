# 👋 Welcome to Specifai Documentation

Specifai is your AI-powered companion for revolutionizing project requirements management! This comprehensive guide will help you master all features of Specifai and transform your SDLC process.

![Specifai Overview](../../static/gif/specifai-overview.gif)

## 📘 What is Specifai?

Specifai is an intelligent requirements management tool that leverages AI to streamline your software development lifecycle. From document generation to user story creation, <PERSON><PERSON>if<PERSON> helps teams work more efficiently and maintain consistency across projects.

## 🏁 Getting Started

New to Specifai? Start here:

📚 [Complete Getting Started Guide](getting-started.md)

- [Installation and Setup](getting-started.md#installation-and-setup)
- [Initial Configuration](getting-started.md#initial-configuration)
- [Workspace Setup](getting-started.md#workspace-setup)
- [Basic Navigation](getting-started.md#basic-navigation)


## 🚀 Core Features

### 🤖 AI-Powered Document Generation
Effortlessly create detailed SDLC documentation with intelligent assistance.

### 💬 Intelligent Chat Interface
Get real-time requirement edits and context-specific suggestions.

<div align="center">

![AI powered chat feature in action](../../static/gif/specifai-chat.gif)
*AI powered chat feature in action*

</div>

### 📊 Requirements Management
Efficiently manage your project requirements with Specifai's robust tools, including:
- Business Process Visualization
- User Story Generation
- Task Management
- BRD-PRD Linking

<div align="center">

![User story and task generation](../../static/gif/specifai-user-stories.gif)
*User story and task generation*

</div>

## 🔌 Enterprise Solutions

### MCP Integration
Specifai leverages powerful MCP Tools including:
- AWS Bedrock Knowledgebase
- FileSystem Integration
- Confluence Integration
- Custom Server Support

### 🔗 Jira Integration
Create and manage epics, stories, and tasks directly in your Jira instance. Support for bidirectional synchronization with pull and push capabilities.

### 🔷 Azure DevOps Integration
Seamlessly synchronize requirements and work items with Azure DevOps. Support for hierarchical work item management with bidirectional sync capabilities.

### ☁️ AWS Bedrock Knowledge Base
Enterprise knowledge base integration for enhanced AI suggestions and iterative conversations.

## 🧠 Model Configuration

### ⚙️ Configuring Preferred AI Models
Choose the AI model that best suits your needs from a variety of providers. Specifai supports:
* **Azure OpenAI**
    * gpt-4o
    * gpt-4o-mini
* **OpenAI Native**
    * gpt-4o
    * gpt-4o-mini
* **AWS Bedrock**
    * anthropic.claude-3-7-sonnet-20250219-v1:0
    * anthropic.claude-3-5-sonnet-20241022-v2:0
    * anthropic.claude-3-5-haiku-20241022-v1:0
    * anthropic.claude-3-5-sonnet-20240620-v1:0
    * anthropic.claude-3-opus-20240229-v1:0
    * anthropic.claude-3-sonnet-20240229-v1:0
    * anthropic.claude-3-haiku-20240307-v1:0
* **Gemini**
    * gemini-2.0-flash-001
    * gemini-2.0-flash-lite-preview-02-05
    * gemini-2.0-pro-exp-02-05
    * gemini-2.0-flash-thinking-exp-01-21
    * gemini-2.0-flash-thinking-exp-1219
    * gemini-2.0-flash-exp
    * gemini-1.5-flash-002
    * gemini-1.5-flash-exp-0827
    * gemini-1.5-flash-8b-exp-0827
    * gemini-1.5-pro-002
    * gemini-1.5-pro-exp-0827
    * gemini-exp-1206
* **OpenRouter**
* **Ollama**

<div align="center">

![Model Switching in Settings](../../static/gif/specifai-settings.gif)
*Easily configurable multi-provider AI model support*

</div>

## 📚 Documentation Sections

### [🎯 Core Features](core-features.md)
- Solution Creation
- Document Generation
- Requirements Management
- User Stories & Tasks
- Model Configuration

### [🔌 Integrations Setup](integrations-setup.md)
- Jira Integration
- Azure DevOps Integration
- AWS Bedrock KB
- MCP Configuration

### [⚡ Advanced Features](advanced-features.md)
- Application Architecture
- Solution Creation Workflow
- User Story Generation Process
- Security and Safety Measures

### [❓ Troubleshooting & FAQs](troubleshooting.md)
- Common Issues
- Integration Problems
- Performance Optimization

## 🔗 Additional Resources

- [GitHub Repository](https://github.com/presidio-oss/specif-ai)
- [Release Notes](https://github.com/presidio-oss/specif-ai/releases)
- [Contributing Guidelines](https://github.com/presidio-oss/specif-ai/blob/main/CONTRIBUTING.md)
- [License Information](https://github.com/presidio-oss/specif-ai/blob/main/LICENSE)

## 🤝 Support & Community

Need assistance? We're here to help:
- 📧 Email: <EMAIL>
- 🐛 [GitHub Issues](https://github.com/presidio-oss/specif-ai/issues)
- 💡 [Feature Requests](https://github.com/presidio-oss/specif-ai/discussions)

---

Specifai is designed to make your development process smoother and more efficient. Our organized documentation ensures you can focus on what matters most – building great solutions!
