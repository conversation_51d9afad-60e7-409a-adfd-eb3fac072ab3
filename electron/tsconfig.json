{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "strict": true, "experimentalDecorators": true, "esModuleInterop": true, "skipLibCheck": true, "sourceMap": true, "noImplicitAny": true, "lib": ["ES2022", "DOM"], "outDir": "build", "rootDir": ".", "typeRoots": ["./node_modules/@types"], "baseUrl": ".", "paths": {"*": ["node_modules/*"]}, "types": ["node"], "allowJs": true, "resolveJsonModule": true}, "include": ["**/*.ts", "**/*.d.ts", "preload.ts"], "exclude": ["node_modules", "dist", "build"]}