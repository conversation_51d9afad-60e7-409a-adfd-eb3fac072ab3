export const UIR_DEFINITION_CONTEXT = `User Interface Requirements (UIR):
- Task: Generate user interface requirements for the following application description.
- Output Format: Each requirement is articulated in a sentence format and avoid using bullet points, numbering and section titles.
- Instructions for Generating UI Requirements:
  User Interactions:
    Identify and describe all key user interactions within the application.
    Specify any particular user flows or processes that need to be supported.
    Highlight any specific actions users must perform and how they interact with various UI components.
  Visual Elements:
    Detail the visual design requirements including layout, colors, fonts, and icons.
    Describe how each screen or page should look and feel.
    Include any specific design patterns or themes that should be applied.
  Functionality:
    List and explain all functional elements required on the user interface, such as buttons, forms, navigation menus, and feedback messages.
    Describe the behavior of dynamic elements like dropdowns, modals, and tooltips.
    Specify any conditions or validations that need to be handled within the UI.
  Accessibility:
    Outline the accessibility features that must be included, such as keyboard navigation, screen reader support, and color contrast requirements.
    Mention any standards or guidelines (e.g., WCAG) that the UI must comply with.
    Describe any additional features to support users with disabilities.
  Performance Considerations:
    Identify performance requirements for the UI, such as load times and responsiveness.
    Mention any considerations for optimizing user experience on various devices and screen sizes.
    Describe any fallback or degradation strategies for low-performance environments.
  User Feedback and Testing:
    Explain how user feedback should be gathered and incorporated into the UI design.
    Specify any usability testing methods that should be used to validate the UI requirements.
    Include details on how iterative improvements based on user testing will be managed.
  Documentation and Guidelines:
    List the documentation that should accompany the UI, such as style guides, design system specifications, and user guides.
    Specify how UI guidelines will be communicated and maintained.`;

export const UIR_CONTEXT = `${UIR_DEFINITION_CONTEXT}

Instructions:
- Generate an apt title for all the following requirements. Title should be a one-liner not more than 5 words.`;
