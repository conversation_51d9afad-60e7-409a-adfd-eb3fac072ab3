{"name": "specif_ai", "version": "2.7.0", "description": "AI-powered requirements management and specification platform", "productName": "Specif AI", "main": "build/app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf build", "check": "tsc --noEmit", "build": "npm run clean && npm run check && tsc --pretty --listEmittedFiles", "start": "npm run build && electron .", "postinstall": "electron-builder install-app-deps", "package:mac": "electron-builder build -m --arm64 --x64 --publish=never", "package:win": "electron-builder build -w --x64 --ia32 --publish=never", "serve:electron": "electron .", "build:ui": "rm -rf ui && cd ../ui && npm run build:dev && cp -r ./dist/ui ../electron/ui/", "build-watch:ui": "rm -rf ui && mkdir ui && cd ../ui && npm run watch:dev", "watch:ui": "concurrently \"npm run build-watch:ui\" \"onchange '../ui/dist/**/*' -- cp -r ../ui/dist/* ./\""}, "engines": {"node": ">=18.17"}, "author": "presidio", "license": "ISC", "devDependencies": {"@electron/fuses": "^1.8.0", "@electron/rebuild": "^3.6.0", "@types/electron": "^1.6.12", "@types/node": "^22.13.10", "@types/semver": "^7.5.8", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "electron": "^36.4.0", "electron-builder": "^24.13.3", "electron-reload": "^2.0.0-alpha.1", "onchange": "^7.1.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.14", "typescript": "^5.8.2"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@aws-sdk/client-bedrock-runtime": "^3.758.0", "@azure/openai": "^2.0.0", "@google/generative-ai": "^0.24.0", "@langchain/anthropic": "^0.3.17", "@langchain/aws": "^0.1.8", "@langchain/community": "^0.3.40", "@langchain/core": "^0.3.44", "@langchain/google-genai": "^0.2.3", "@langchain/langgraph": "^0.2.63", "@langchain/ollama": "^0.2.0", "@langchain/openai": "^0.5.5", "@modelcontextprotocol/sdk": "^1.9.0", "@presidio-dev/hai-guardrails": "^1.9.0", "@sentry/electron": "^5.3.0", "@smithy/eventstream-codec": "^2.2.0", "@smithy/signature-v4": "^2.3.0", "axios": "^1.7.7", "cheerio": "^1.1.0", "dotenv": "^16.4.7", "electron-notarize": "^1.2.2", "electron-squirrel-startup": "^1.0.1", "electron-store": "^8.2.0", "electron-updater": "^6.3.9", "express": "^4.21.0", "jsonrepair": "^3.12.0", "langchain": "^0.3.19", "langfuse": "^3.37.0", "mammoth": "^1.9.1", "node-fetch": "^3.3.2", "openai": "^4.87.4", "shell-path": "^3.0.0", "zod": "^3.24.2"}, "build": {"files": ["build/**/*"], "appId": "", "extraResources": [".env", "ui"], "mac": {"forceCodeSigning": true, "category": "public.app-category.utilities", "icon": "assets/icons/hai_icon.icns", "gatekeeperAssess": false, "notarize": {"teamId": "9NENB68LF9"}, "hardenedRuntime": true, "target": ["dmg", "zip"], "entitlements": "build-assets/entitlements.mac.plist", "entitlementsInherit": "build-assets/entitlements.mac.plist"}, "dmg": {"sign": false}, "win": {"icon": "assets/icons/hai_icon.ico"}, "linux": {"icon": "assets/icons/hai_icon.png"}, "publish": [{"provider": "github", "owner": "presidio-oss", "repo": "specif-ai"}]}}