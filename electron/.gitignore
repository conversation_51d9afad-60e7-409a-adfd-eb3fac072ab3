# Dependencies
node_modules/
npm-debug.log
yarn-error.log

# Build output
dist/
build/
ui/

# Environment files
.env
.env.local
.env.*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# VS Code directories and files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# OS generated files
.DS_Store
Thumbs.db

# Electron-specific
.electron-vue/

# Ignore test coverage output
coverage/

# Ignore temporary files
*.tmp
