# RIS - 完整实现规格说明书

## 1. 项目总览（Project Overview）

### 1.1 产品核心能力总结

Specifai 是一个基于 AI 的需求管理平台，核心能力包括：
- **AI 驱动的文档生成**：自动生成 BRD、PRD、NFR、UIR 等 SDLC 文档
- **智能聊天界面**：提供实时需求编辑和上下文相关建议
- **业务流程可视化**：生成和管理业务流程图
- **用户故事和任务管理**：将需求转换为可执行的用户故事和任务
- **多平台集成**：支持 Jira、Azure DevOps 等项目管理工具集成
- **多模型支持**：支持 OpenAI、Azure OpenAI、AWS Bedrock、Anthropic、Gemini 等多种 AI 模型

### 1.2 代码规模统计

| 指标 | 数量 |
|------|------|
| TypeScript 文件数 | 255 |
| HTML 模板文件数 | 73 |
| SCSS 样式文件数 | 59 |
| TypeScript 代码行数 | 31,835 |
| 测试文件数 | 55 |
| 测试覆盖率 | 约 21.6% (55/255) |

### 1.3 主要第三方 Angular 库

| 库名 | 版本 | 用途 |
|------|------|------|
| @angular/material | ^16.2.12 | UI 组件库 |
| @ngxs/store | ^18.0.0 | 状态管理 |
| @ng-icons/core | ^28.1.0 | 图标库 |
| @tiptap/core | ^2.11.5 | 富文本编辑器 |
| ngx-loading | ^16.0.0 | 加载指示器 |
| ngx-smart-modal | ^14.0.3 | 模态框组件 |
| rxjs | ~7.8.0 | 响应式编程 |

### 1.4 Electron IPC 通道分析

#### 核心通道（高频使用）
| 通道名 | 调用频率 | 数据体积 | 用途 |
|--------|----------|----------|------|
| `core:chat` | 高 | 中等 (1-10KB) | AI 聊天交互 |
| `core:getSuggestions` | 高 | 小 (<1KB) | 获取 AI 建议 |
| `solution:createSolution` | 中 | 大 (10-100KB) | 创建解决方案 |
| `requirement:update` | 中 | 中等 (1-10KB) | 更新需求 |
| `story:create` | 中 | 中等 (1-10KB) | 创建用户故事 |

#### 文件系统通道
| 通道名 | 调用频率 | 数据体积 | 用途 |
|--------|----------|----------|------|
| `dialog:openFile` | 低 | 小 | 打开文件对话框 |
| `dialog:saveFile` | 低 | 变化 (1KB-1MB) | 保存文件 |
| `dialog:openDirectory` | 低 | 小 | 选择目录 |
| `store-get/set` | 中 | 小-中等 | 本地存储操作 |

#### 集成通道
| 通道名 | 调用频率 | 数据体积 | 用途 |
|--------|----------|----------|------|
| `start-jira-oauth` | 低 | 小 | Jira OAuth 认证 |
| `validate-ado-credentials` | 低 | 小 | Azure DevOps 验证 |
| `mcp:*` | 中 | 中等 | MCP 服务器管理 |

## 2. 功能-路由-状态三元组映射表（FRM）

### 2.1 Angular Router 到 React Router v6 映射

| Angular 路由 | React Router v6 路由 | 功能描述 | 全局状态切片 | 权限守卫转换 |
|-------------|---------------------|----------|-------------|-------------|
| `/login` | `/login` | 用户登录页面 | `authSlice` | `<PublicRoute>` |
| `/apps` | `/apps` | 项目列表页面 | `projectsSlice` | `<RequireAuth>` |
| `/apps/create` | `/apps/create` | 创建解决方案 | `projectsSlice`, `workflowSlice` | `<RequireAuth>` + `canDeactivate` |
| `/apps/:id` | `/apps/:id` | 项目详情页面 | `projectsSlice`, `requirementsSlice` | `<RequireAuth>` |
| `/user-stories/:prdId` | `/user-stories/:prdId` | 用户故事列表 | `userStoriesSlice` | `<RequireAuth>` |
| `/task-list/:userStoryId` | `/tasks/:userStoryId` | 任务列表 | `tasksSlice` | `<RequireAuth>` |
| `/task/:mode/:userStoryId/:taskId?` | `/tasks/:mode/:userStoryId/:taskId?` | 任务编辑 | `tasksSlice` | `<RequireAuth>` + `canDeactivate` |
| `/story/:mode/:userStoryId?` | `/stories/:mode/:userStoryId?` | 故事编辑 | `userStoriesSlice` | `<RequireAuth>` + `canDeactivate` |
| `/edit` | `/requirements/edit` | 需求编辑 | `requirementsSlice` | `<RequireAuth>` + `canDeactivate` |
| `/bp-add` | `/business-process/add` | 业务流程添加 | `businessProcessSlice` | `<RequireAuth>` + `canDeactivate` |
| `/bp-flow/:mode/:id` | `/business-process/flow/:mode/:id` | 业务流程图 | `businessProcessSlice` | `<RequireAuth>` |
| `/settings` | `/settings` | 设置页面 | `settingsSlice` | `<RequireAuth>` |
| `/test-cases/:userStoryId` | `/test-cases/:userStoryId` | 测试用例列表 | `testCasesSlice` | `<RequireAuth>` |
| `/strategic-initiative/:mode/:fileName?` | `/strategic-initiative/:mode/:fileName?` | 战略倡议 | `strategicInitiativeSlice` | `<RequireAuth>` + `canDeactivate` |

### 2.2 路由守卫转换策略

#### Angular Guards → React Router Loaders/Components

```typescript
// Angular UserGuard → React RequireAuth Component
const RequireAuth = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, hasWorkingDir } = useAuth();
  
  if (!hasWorkingDir) {
    // 重定向到设置页面
    return <Navigate to="/setup" replace />;
  }
  
  if (!isAuthenticated) {
    // 显示用户配置对话框
    return <UserProfileDialog />;
  }
  
  return <>{children}</>;
};

// Angular CanDeactivateGuard → React Router beforeUnload
const useCanDeactivate = (hasUnsavedChanges: boolean) => {
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);
};
```

## 3. 组件-服务-依赖映射表（CSD）

### 3.1 核心页面组件映射

| Angular 组件 | Selector | 主要 @Input | 主要 @Output | 依赖服务 | React 等效实现 |
|-------------|----------|-------------|-------------|----------|---------------|
| `AppsComponent` | `app-apps` | - | - | `Store`, `Router`, `ElectronService` | `AppsPage` + `useAppSelector`, `useNavigate`, `useElectron` |
| `CreateSolutionComponent` | `app-create-solution` | - | - | `Store`, `ElectronService`, `FormBuilder` | `CreateSolutionPage` + `useForm`, `useElectron` |
| `AppInfoComponent` | `app-info` | - | - | `Store`, `ElectronService` | `ProjectDetailPage` + hooks |
| `UserStoriesComponent` | `app-user-stories` | - | - | `Store`, `SearchService` | `UserStoriesPage` + `useSearch` |
| `LoginComponent` | `app-login` | - | - | `UserStateService` | `LoginPage` + `useAuth` |

### 3.2 核心 UI 组件映射

| Angular 组件 | 主要 @Input | 主要 @Output | React 等效实现 |
|-------------|-------------|-------------|---------------|
| `InputFieldComponent` | `elementId`, `elementPlaceHolder`, `required` | `enterPressed` | `<Input>` + `forwardRef` |
| `ButtonComponent` | `variant`, `size`, `disabled` | `click` | `<Button>` + Tailwind variants |
| `AiChatComponent` | `chatType`, `baseContent`, `chatHistory` | - | `<AiChat>` + `useChat` hook |
| `UnifiedCardComponent` | `title`, `description`, `badges` | `viewItem`, `copyContent` | `<Card>` + compound pattern |
| `SearchInputComponent` | `placeholder` | `searchChange` | `<SearchInput>` + `useDebounce` |

### 3.3 Angular 服务到 React 实现映射

| Angular 服务 | 作用域 | 主要功能 | React 实现方案 |
|-------------|--------|----------|---------------|
| `ElectronService` | root | Electron IPC 通信 | `useElectron` hook + context |
| `ToasterService` | root | 消息提示 | `react-hot-toast` + `useToast` |
| `DialogService` | root | 模态框管理 | `@radix-ui/react-dialog` + context |
| `LoadingService` | root | 加载状态 | `loadingSlice` + `useLoading` |
| `SearchService` | root | 搜索过滤 | `useSearch` hook + `useMemo` |
| `UtilityService` | root | 工具函数 | `/src/utils/*.ts` 纯函数 |
| `AppSystemService` | root | 文件系统操作 | `fileSystemSlice` + RTK Query |
| `WorkflowProgressService` | root | 工作流进度 | `workflowSlice` + `useWorkflowProgress` |

### 3.4 NGXS 状态管理到 Redux Toolkit 映射

| NGXS State | 主要 Selectors | 主要 Actions | Redux Toolkit Slice |
|-----------|---------------|-------------|-------------------|
| `ProjectsState` | `getProjects`, `getSelectedProject` | `GetProjectListAction`, `CreateProject` | `projectsSlice` |
| `UserStoriesState` | `getUserStories`, `getSelectedUserStory` | `CreateNewUserStory`, `UpdateUserStory` | `userStoriesSlice` |
| `BreadcrumbState` | `getBreadcrumbs` | `AddBreadcrumb`, `DeleteBreadcrumb` | `breadcrumbSlice` |
| `BusinessProcessState` | `getSelectedFlowChart` | `GetFlowChartAction` | `businessProcessSlice` |
| `LLMConfigState` | `getConfig`, `getActiveProvider` | `SetLLMConfig`, `VerifyLLMConfig` | `llmConfigSlice` |
| `ChatSettingsState` | `getChatSettings` | `UpdateChatSettings` | `chatSettingsSlice` |

### 3.5 RxJS Observable 替换方案

| RxJS 模式 | 使用场景 | React 替换方案 |
|-----------|----------|---------------|
| `BehaviorSubject` | 状态管理 | Redux Toolkit state |
| `Subject` | 事件发射 | `useReducer` dispatch |
| `Observable.pipe(map, filter)` | 数据转换 | `useMemo` + 纯函数 |
| `combineLatest` | 多流合并 | `useSelector` 多个状态 |
| `switchMap` | HTTP 请求 | RTK Query `queryFn` |
| `debounceTime` | 防抖 | `useDebounce` hook |
| `distinctUntilChanged` | 去重 | `useMemo` 依赖数组 |
| `takeUntil` | 取消订阅 | `useEffect` cleanup |

