<div class="relative inline-block text-left" *ngIf="groupedOptions && groupedOptions.length > 0 && hasAnyOptions()" #dropdownContainer>
  <app-button
  [disabled]="disabled"
  buttonContent="{{ buttonLabel }}"
  [icon]="isOpen ? 'heroChevronDown' : 'heroChevronRight'"
  theme="secondary"
  size="sm"
  rounded="lg"
  (click)="toggleDropdown($event)"
  class="export-button"
  >
  </app-button>
  <div *ngIf="isOpen"
       class="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none export-dropdown"
       role="menu"
       aria-orientation="vertical">
    <div *ngIf="groupedOptions && groupedOptions.length > 0" class="py-1" role="none">
      <ng-container *ngFor="let group of groupedOptions; let last = last">
        <div class="px-4 py-2 text-xs font-semibold">
          {{ group.groupName }}
        </div>
            <button
          *ngFor="let option of group.options"
          class="block w-full px-4 py-3 text-left text-sm text-secondary-800 hover:bg-gray-100"
          role="menuitem"
          type="button"
          (mousedown)="$event.preventDefault()"
          (click)="onOptionClick(option, $event)"
          (mouseup)="$event.preventDefault()"
        >
          <div class="flex items-center">
            <ng-icon *ngIf="option.icon" [name]="option.icon" class="mr-2 text-secondary-500"></ng-icon>
            <div class="flex flex-col">
              <span class="text-sm">{{ option.label }}</span>
              <div *ngIf="option.additionalInfo" class="flex items-center text-[10px] text-secondary-500 mt-0.5 whitespace-nowrap">
                  <span *ngIf="option.isTimestamp">Last Synced: {{ option.additionalInfo | timezone }}</span>
                  <span *ngIf="!option.isTimestamp">{{ option.additionalInfo }}</span>
              </div>
            </div>
          </div>
        </button>
        <div *ngIf="!last" class="border-t border-gray-200 my-1"></div>
      </ng-container>
    </div>
  </div>
</div>
