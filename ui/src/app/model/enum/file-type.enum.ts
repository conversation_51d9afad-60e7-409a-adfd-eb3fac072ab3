export enum FileTypeEnum {
  BRD = 'Business Requirements',
  PRD = 'Product Requirements',
  UIR = 'User Interface Requirements',
  NFR = 'Non Functional Requirements',
  BP = 'Business Process',
  SI = 'Strategic Initiatives',
  TC = 'Test Cases',
}

export enum IconPairingEnum {
  BRD = 'heroBriefcase',
  PRD = 'heroSquares2x2',
  UIR = 'heroCube',
  NFR = 'heroWindow',
  BP = 'heroSquare3Stack3d',
  SI = 'heroPresentationChartBar',
  TC = 'heroBeaker',
}
