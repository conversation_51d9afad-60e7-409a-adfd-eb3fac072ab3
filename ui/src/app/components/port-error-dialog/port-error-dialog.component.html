<div class="bg-white rounded-lg shadow-md max-w-lg">
  <div class="flex items-center justify-between p-4 border-b border-gray-300">
    <h4 class="text-lg font-medium">Port Error</h4>
    <button
      (click)="closeDialog()"
      class="close-icon absolute top-4 right-4 text-gray-500 hover:text-gray-700"
    >
      <ng-icon name="heroXMark" class="text-lg"></ng-icon>
    </button>
  </div>
  <div class="p-4">
    <p class="text-sm text-gray-500">
      The host port is currently in use by another application. To proceed,
      please close the conflicting application. Keep in mind that Jira
      integration may not function correctly until this issue is resolved.
    </p>
  </div>
  <div class="flex justify-end gap-4 pb-4 px-4">
    <button
      mat-button
      (click)="closeDialog()"
      class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200"
    >
      Close
    </button>
    <button
      mat-button
      (click)="killPort()"
      class="bg-danger-100 text-danger-600 hover:bg-danger-200 px-4 py-2 rounded-md"
    >
      Stop Instance
    </button>
  </div>
</div>
