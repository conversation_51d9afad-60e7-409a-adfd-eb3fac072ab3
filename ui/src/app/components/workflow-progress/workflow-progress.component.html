<div *ngIf="isVisible" class="max-w-4xl mx-auto h-full flex flex-col">
  <div
    *ngIf="showHeader"
    class="sticky top-0 z-50 bg-white border-b border-gray-300 px-4 py-3 flex items-center justify-between"
  >
    <div class="flex items-center gap-3">
      <h1
        class="text-base font-bold text-gray-900 truncate leading-tight tracking-tight"
      >
        {{ isCompleted ? completedTitle : initialTitle }}
      </h1>

      <button
        *ngIf="showCancelButton && (workflowStatus$ | async)?.isCreating"
        (click)="onCancelClick()"
        [disabled]="isAborting()"
        class="flex items-center justify-center text-red-500 hover:text-red-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed p-1.5 rounded-full hover:bg-red-50 hover:shadow-sm border border-transparent hover:border-red-100 group"
        type="button"
        title="Cancel Process"
      >
        <ng-icon
          *ngIf="!isAborting()"
          name="heroStopCircle"
          size="20"
          class="transition-transform duration-200 group-hover:scale-110"
        ></ng-icon>
      </button>
    </div>

    <div class="flex items-center gap-2">
      <app-button
        *ngIf="hasAnyAccordionEvents$ | async"
        (click)="toggleExpandAll()"
        [buttonContent]="isExpandedAll() ? 'Collapse All' : 'Expand All'"
        [icon]="
          isExpandedAll() ? 'heroChevronDoubleUp' : 'heroChevronDoubleDown'
        "
        theme="secondary_outline"
        size="xs"
        rounded="lg"
        type="button"
      ></app-button>

      <div class="flex items-center" *ngIf="!isCompleted">
        <div
          class="flex items-center text-xs font-medium bg-gray-50 rounded-lg px-2 py-1 border border-gray-200 shadow-sm"
        >
          <div
            class="w-1.5 h-1.5 bg-primary-600 rounded-full mr-2 animate-pulse"
          ></div>
          <span class="whitespace-nowrap">{{ subtitle }}</span>
        </div>
      </div>
    </div>
  </div>

  <div
    class="flex-1 p-4 overflow-y-auto"
    *ngIf="processedProgress$ | async as processedSteps"
  >
    <div
      *ngIf="processedSteps.length > 0; else loaderTemplate"
      class="space-y-1"
      [style.max-height]="isExpandedAll() ? 'none' : maxHeight"
    >
      <div *ngFor="let step of processedSteps; let i = index" class="group">
        <div
          *ngIf="!step.hasInputOutput"
          class="flex items-center space-x-3 py-2 px-3 rounded-md transition-all duration-200 border border-secondary-200"
        >
          <ng-container
            [ngTemplateOutlet]="stepIconTemplate"
            [ngTemplateOutletContext]="{
              step: step,
              showSpinner: false,
            }"
          ></ng-container>

          <div class="flex-1 min-w-0 flex items-center justify-between">
            <span
              class="text-sm leading-relaxed flex-1 truncate pr-2 flex items-center"
              [ngClass]="step.textColorClass"
            >
              {{ step.message.title }}
            </span>

            <div
              *ngIf="step.shouldShowSpinner"
              class="inline-block w-3 h-3 border-2 border-t-transparent rounded-full animate-spin flex-shrink-0"
              [ngClass]="step.borderColorClass"
              role="status"
              aria-label="Executing"
            ></div>
          </div>
        </div>

        <app-custom-accordion
          *ngIf="step.hasInputOutput"
          [id]="step.accordionId"
          [isOpen]="shouldAccordionBeOpen(step.accordionId)"
          (toggleChange)="onAccordionToggle(step.accordionId, $event)"
          triggerClassName="py-2 px-3 rounded-md cursor-pointer transition-all duration-200"
          bodyClassName="px-3 pb-3 mt-2"
        >
          <div
            accordion-trigger
            class="flex items-center space-x-3 min-w-0 w-full"
          >
            <ng-container
              [ngTemplateOutlet]="stepIconTemplate"
              [ngTemplateOutletContext]="{
                step: step,
              }"
            ></ng-container>

            <div class="flex-1 min-w-0 flex items-center justify-between">
              <p
                class="text-sm leading-relaxed flex-1 truncate pr-2 min-w-0 flex items-center"
                [ngClass]="step.textColorClass"
              >
                {{ step.message.title }}
              </p>
            </div>
          </div>

          <div
            accordion-body
            class="space-y-3 mt-3 border-t border-gray-100 pt-3"
          >
            <div *ngIf="step.formattedInput">
              <ng-container
                [ngTemplateOutlet]="stepDataTemplate"
                [ngTemplateOutletContext]="{
                  title: 'Input',
                  data: step.formattedInput,
                }"
              ></ng-container>
            </div>

            <div *ngIf="step.formattedOutput">
              <ng-container
                [ngTemplateOutlet]="stepDataTemplate"
                [ngTemplateOutletContext]="{
                  title: 'Output',
                  data: step.formattedOutput,
                }"
              ></ng-container>
            </div>
          </div>
        </app-custom-accordion>
      </div>
    </div>
  </div>

  <ng-template #stepIconTemplate let-step="step">
    <div class="flex-shrink-0 relative flex items-center justify-center">
      <div
        class="absolute inset-0 rounded-full blur-md opacity-30"
        [ngClass]="step.colorClass"
      ></div>
      <ng-icon
        [name]="step.iconName"
        class="h-5 w-5 shrink-0 relative"
        [class.text-primary-500]="
          step.type === WorkflowProgressEventType.Thinking
        "
        [class.text-success-500]="
          step.type === WorkflowProgressEventType.Action
        "
        [class.text-amber-500]="step.type === WorkflowProgressEventType.Mcp"
        [class.text-red-500]="step.type === WorkflowProgressEventType.Error"
      >
      </ng-icon>
    </div>
  </ng-template>

  <ng-template #stepDataTemplate let-title="title" let-data="data">
    <div class="space-y-2">
      <div class="flex items-center space-x-2">
        <h4 class="text-xs font-semibold text-gray-600 uppercase tracking-wide">
          {{ title }}
        </h4>
        <div class="flex-1 h-px bg-gray-200"></div>
      </div>
      <div
        class="bg-gray-50 border border-gray-200 rounded-lg p-3 overflow-hidden"
      >
        <pre
          class="text-xs text-gray-700 whitespace-pre-wrap font-mono leading-relaxed overflow-x-auto max-w-full"
          >{{ data }}</pre
        >
      </div>
    </div>
  </ng-template>

  <ng-template #loaderTemplate>
    <div class="flex flex-col items-center justify-center py-8 space-y-4">
      <h3 class="text-sm font-semibold text-gray-800 tracking-wide">
        We will be ready to proceed in a moment
      </h3>
      <div class="pt-1" style="--dot-size: 4px; --dot-color: #312e81">
        <app-three-bounce-loader> </app-three-bounce-loader>
      </div>
    </div>
  </ng-template>
</div>
