<div
  *ngIf="isVisible"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm overlay-enter overflow-y-auto"
  [class.animate-fadeIn]="isVisible"
>
  <div
    class="bg-white rounded-xl p-6 pb-3 shadow-2xl max-w-3xl w-full mx-4 my-8 max-h-[90vh] border border-gray-200 workflow-dialog flex flex-col overflow-hidden"
  >
    <div class="flex-1 overflow-y-auto">
      <app-workflow-progress
        [projectId]="projectId"
        [workflowType]="workflowType"
        [isVisible]="true"
        [isCompleted]="isCompleted"
        [initialTitle]="initialTitle"
        [completedTitle]="completedTitle"
        [subtitle]="subtitle"
        [showCancelButton]="showCancelButton"
      >
      </app-workflow-progress>
    </div>

    <div
      *ngIf="isCompleted"
      class="p-4 flex justify-center border-t border-gray-300"
    >
      <app-button
        [buttonContent]="completionButtonText"
        theme="primary"
        size="sm"
        rounded="lg"
        icon="heroArrowRight"
        iconPosition="right"
        (click)="onCloseDialog()"
      />
    </div>
  </div>
</div>
