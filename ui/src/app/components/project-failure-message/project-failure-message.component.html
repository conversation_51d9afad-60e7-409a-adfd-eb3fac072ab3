<div
  class="mx-2 mt-1 mb-2 animate-in slide-in-from-top-4 duration-700 ease-out"
>
  <div
    class="relative overflow-hidden rounded-lg border border-orange-200/50 bg-gradient-to-br from-orange-50/60 via-amber-50/40 to-yellow-50/30 p-3 shadow-md shadow-orange-100/20 backdrop-blur-sm"
    role="alert"
    aria-live="polite"
    aria-labelledby="failure-title"
    aria-describedby="failure-description"
  >
    <div
      class="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent opacity-50"
    ></div>

    <div class="relative">
      <div class="flex items-start space-x-2.5">
        <div
          class="flex-shrink-0 animate-in zoom-in-50 duration-1000 delay-200"
        >
          <div
            class="relative flex h-7 w-7 items-center justify-center rounded-lg bg-gradient-to-br from-orange-100/90 via-amber-50/70 to-yellow-50/50 ring-1 ring-orange-200/40 shadow-sm"
          >
            <ng-icon
              class="relative h-4 w-4 text-orange-600 drop-shadow-sm"
              name="heroExclamationTriangle"
              strokeWidth="1.5"
            ></ng-icon>
          </div>
        </div>

        <div class="flex-1 min-w-0 space-y-2.5">
          <div class="animate-in slide-in-from-left-4 duration-800 delay-300">
            <div class="flex items-center justify-between">
              <div>
                <h3
                  id="failure-title"
                  class="text-base font-semibold text-gray-900 mb-0.5 tracking-tight leading-tight"
                >
                  Unable to Generate Solution Requirements
                </h3>
                <p class="text-xs text-gray-600 font-medium">
                  We encountered an issue while setting up your solution
                </p>
              </div>
            </div>
          </div>

          <div class="animate-in slide-in-from-left-4 duration-800 delay-400">
            <div class="flex flex-wrap items-center gap-1.5">
              <div
                class="inline-flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-slate-100/90 to-gray-100/70 rounded-md border border-slate-200/60 shadow-sm backdrop-blur-sm"
                *ngIf="formattedFailureTimestamp"
              >
                <ng-icon
                  class="h-3 w-3 text-slate-600"
                  name="heroClock"
                  strokeWidth="2"
                ></ng-icon>
                <span
                  class="text-xs font-medium text-slate-700 tracking-wide"
                  >{{ formattedFailureTimestamp }}</span
                >
              </div>
            </div>
          </div>

          <div class="animate-in slide-in-from-left-4 duration-800 delay-500">
            <div
              class="relative overflow-hidden rounded-xl bg-gradient-to-br from-white via-slate-50/20 to-white border border-slate-200/70 shadow-lg shadow-slate-100/30 backdrop-blur-sm"
            >
              <div
                class="absolute inset-0 bg-gradient-to-br from-blue-50/15 via-indigo-50/10 to-slate-50/15 rounded-xl"
              ></div>
              <div
                class="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-slate-200/60 to-transparent"
              ></div>

              <div class="relative p-2">
                <div class="flex items-start gap-2">
                  <div class="flex-1 space-y-1">
                    <div class="flex items-center gap-2">
                      <span
                        class="inline-flex items-center gap-1 px-1.5 py-1 rounded-lg bg-orange-100/80 border border-orange-200/60 text-xs font-semibold text-orange-800 shadow-sm"
                      >
                        <ng-icon
                          class="h-2 w-2 text-orange-700"
                          name="heroExclamationTriangle"
                        ></ng-icon>
                        Error Details
                      </span>
                      <div
                        class="flex-1 h-px bg-gradient-to-r from-slate-200/60 to-transparent"
                      ></div>
                    </div>
                    <div class="relative">
                      <p
                        id="failure-description"
                        class="text-sm text-slate-700 leading-relaxed font-medium pl-1"
                      >
                        {{ failureReason }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="animate-in slide-in-from-left-4 duration-800 delay-700">
            <div class="flex flex-wrap gap-2 pt-1">
              <app-button
                buttonContent="Try Again"
                theme="primary"
                size="sm"
                rounded="lg"
                (click)="onRetryClick()"
                class="shadow-md shadow-blue-100/50 font-medium"
              />

              <app-button
                *ngIf="hasLogs"
                [buttonContent]="logsButtonText"
                theme="secondary"
                size="sm"
                rounded="lg"
                [icon]="logsButtonIcon"
                (click)="toggleLogs()"
                class="shadow-md shadow-gray-100/50 font-medium border-gray-300 text-gray-700 hover:bg-gray-50"
              />

              <app-button
                buttonContent="Create New Project"
                theme="secondary"
                size="sm"
                rounded="lg"
                icon="heroPlus"
                (click)="onCreateNewProjectClick()"
                class="shadow-md shadow-gray-100/50 font-medium"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div
    *ngIf="showLogs && hasLogs"
    class="mt-2 animate-in slide-in-from-top-4 duration-500 ease-out"
  >
    <div
      class="relative overflow-hidden rounded-lg border border-gray-200/40 bg-gradient-to-br from-gray-50/60 via-slate-50/40 to-white/30 shadow-md shadow-gray-100/20 backdrop-blur-md"
    >
      <div class="relative px-3 py-2 border-b border-gray-200/30">
        <div
          class="absolute inset-0 bg-gradient-to-r from-gray-100/20 to-slate-100/20"
        ></div>
        <div class="relative flex items-center justify-between">
          <div class="flex items-center gap-2">
            <div
              class="flex h-6 w-6 items-center justify-center rounded-md bg-gradient-to-br from-gray-100/80 via-gray-50/60 to-slate-50/40 ring-1 ring-gray-200/50 shadow-sm"
            >
              <ng-icon
                class="h-3 w-3 text-gray-600"
                name="heroDocumentText"
                strokeWidth="2"
              ></ng-icon>
            </div>
            <div>
              <h4 class="text-sm font-semibold text-gray-900 leading-tight">
                Workflow Execution Logs
              </h4>
            </div>
          </div>

          <button
            (click)="toggleLogs()"
            class="flex items-center gap-1.5 px-2.5 py-1.5 text-xs font-medium text-gray-700 bg-gray-50/80 border border-gray-200/50 rounded-lg hover:bg-gray-100/80 hover:border-gray-300/50 transition-all duration-200 shadow-sm"
            type="button"
          >
            <ng-icon
              class="h-3 w-3"
              name="heroChevronUp"
              strokeWidth="2"
            ></ng-icon>
            <span>Collapse</span>
          </button>
        </div>
      </div>

      <div class="relative p-3">
        <div class="relative">
          <div class="workflow-logs-container">
            <app-workflow-progress
              [projectId]="projectId"
              [workflowType]="workflowType"
              [isVisible]="true"
              [isCompleted]="true"
              [maxHeight]="'20rem'"
              [showCancelButton]="false"
              [showHeader]="false"
            ></app-workflow-progress>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
