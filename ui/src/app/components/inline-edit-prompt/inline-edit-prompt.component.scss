:host {
  display: block;
}

::ng-deep .inline-edit-dialog .mat-mdc-dialog-container {
  border-radius: 12px !important;
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 8px 10px -6px rgba(0, 0, 0, 0.2) !important;
}

::ng-deep .inline-edit-dialog .mdc-dialog__surface {
  background-color: #1f2937 !important;
  color: white;
  border: none !important;
}

::ng-deep .inline-edit-dialog .mdc-dialog__container {
  backdrop-filter: blur(10px);
}

::ng-deep .inline-edit-backdrop {
  background: rgba(255, 255, 255, 0.5);
}


// Textarea auto-resize
textarea.resize-none {
  overflow-y: hidden;
  min-height: 28px;
  transition: height 0.1s ease;
}

// Dialog animation
.prompt-enter {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
