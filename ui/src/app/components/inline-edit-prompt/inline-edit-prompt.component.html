<div class="relative bg-white backdrop-blur-md rounded-xl p-4 shadow-lg border border-secondary-200">
  <!-- Beta tag -->
  <div class="absolute top-2 right-2 px-2 py-0.5 text-[10px] font-semibold bg-primary-100 text-primary-700 rounded uppercase tracking-wide">
    Beta
  </div>

  <!-- Initial prompt mode -->
  <form *ngIf="!isLoading && !isEditReady" [formGroup]="promptForm" (ngSubmit)="onSubmit(); $event.preventDefault();" class="flex flex-col gap-3">
    
    <div *ngIf="selectedText">
      <p class="text-xs text-muted-foreground mb-1">Selected text:</p>
      <div class="text-sm text-foreground bg-secondary-100 rounded p-2 max-h-24 overflow-y-auto">
        {{ selectedText }}
      </div>
    </div>
    
    <!-- Input group -->
    <div class="flex items-center bg-secondary-100 rounded-lg px-3 py-2 focus-within:ring-1 focus-within:ring-primary-400 transition-all">
      <div class="flex-shrink-0 mr-2">
        <ng-icon name="heroSparklesSolid" class="text-primary-500 w-5 h-5"></ng-icon>
      </div>
      <textarea
        id="userPrompt"
        formControlName="userPrompt"
        rows="1"
        class="w-full bg-transparent text-foreground border-none focus:outline-none focus:ring-0 resize-none placeholder-secondary-400 py-1"
        placeholder="Edit or explain..."
        (input)="autoResize($event)"
      ></textarea>
      <button 
        type="submit" 
        [disabled]="promptForm.invalid"
        class="ml-2 flex-shrink-0 flex items-center justify-center w-6 h-6 rounded-full bg-primary-600 hover:bg-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          <ng-icon name="heroArrowRight" class="text-white w-3 h-3"></ng-icon>
      </button>
    </div>

    <ng-container *ngIf="promptForm.get('userPrompt')?.invalid && promptForm.get('userPrompt')?.touched">
      <span class="text-danger-500 text-xs">Please enter a prompt</span>
    </ng-container>

    <div *ngIf="errorMessage" class="p-2 bg-danger-100 border border-danger-200 rounded text-danger-700 text-sm">
      {{ errorMessage }}
    </div>
  </form>

  <!-- Loading state -->
  <div *ngIf="isLoading" class="flex flex-col items-center py-6 space-y-2">
    <div class="relative w-10 h-10">
      <div class="absolute top-0 left-0 w-full h-full border-2 border-primary-300/20 rounded-full"></div>
      <div class="absolute top-0 left-0 w-full h-full border-t-2 border-r-2 border-primary-500 rounded-full animate-spin"></div>
    </div>
    <p class="text-foreground">Processing your edit...</p>
    <p class="text-muted-foreground text-sm">Please wait, this may take a moment</p>
    <button 
      (click)="goBackToPrompt()" 
      class="px-3 py-1.5 text-sm bg-secondary-200 border border-secondary-300 text-foreground rounded-md hover:bg-secondary-300 transition-colors">
      Cancel
    </button>
  </div>

  <!-- Review state -->
  <div *ngIf="!isLoading && isEditReady" class="flex flex-col gap-4">
    <div>
      <p class="text-xs text-muted-foreground mb-1">Original text:</p>
      <div class="text-sm text-foreground bg-secondary-100 rounded p-2 max-h-28 overflow-y-auto">
        {{ selectedText }}
      </div>

      <p class="text-xs text-muted-foreground mb-1">Suggested edit:</p>
      <div class="text-sm text-foreground bg-primary-50 border border-primary-200 rounded p-2 max-h-36 overflow-y-auto prose prose-sm max-w-none prose-secondary-edit">
        <div [innerHTML]="renderedHtml"></div>
      </div>
    </div>

    <div class="flex justify-between items-center">
      <button 
        (click)="goBackToPrompt()" 
        class="px-3 py-1.5 text-sm bg-secondary-200 border border-secondary-300 text-foreground rounded-md hover:bg-secondary-300 transition-colors">
        Try again
      </button>
      <div class="flex space-x-2">
        <button 
          (click)="rejectEdit()" 
          class="px-3 py-1.5 text-sm bg-secondary-200 border border-secondary-300 text-foreground rounded-md hover:bg-secondary-300 transition-colors">
          Reject
        </button>
        <button 
          (click)="acceptEdit()" 
          class="px-3 py-1.5 text-sm bg-primary-600 text-white rounded-md hover:bg-primary-500 transition-colors">
          Apply edit
        </button>
      </div>
    </div>
  </div>
</div>
