<div class="p-6 flex flex-col justify-start">
  <div>
    <h1 class="text-2xl font-semibold mb-6">{{ data.title }}</h1>
  </div>
  <div class="rounded-md" [formGroup]="emittedForm">
    <label class="block text-secondary-700 text-sm font-medium mb-2">
      {{ data.description }} <span class="text-secondary-400">(Optional)</span>
    </label>
    <app-textarea-field
      [elementPlaceHolder]="data.placeholder"
      elementId="extraContext"
      formControlName="extraContext"
    />
  </div>
  <div class="flex justify-between mt-6">
    <app-button
      buttonContent="Cancel"
      theme="secondary"
      size="sm"
      rounded="lg"
      (click)="onClose()"
    />
    <app-button
      buttonContent="Generate"
      theme="primary"
      size="sm"
      rounded="lg"
      (click)="onGenerate()"
    />
  </div>
</div>
