<div class="fixed bottom-4 right-4 z-50 flex flex-col-reverse gap-2">
  <div
    *ngFor="let toast of toasts"
    class="text-xs transition-opacity duration-300 ease-in-out"
    [ngClass]="{ 'opacity-100': toast.show, 'opacity-0': !toast.show }"
    (click)="removeToast(toast.id)"
    (keydown.enter)="removeToast(toast.id)"
    (keydown.space)="removeToast(toast.id); $event.preventDefault()"
  >
    <div
      class="w-72 p-4 rounded-lg border-[0.5px]"
      [ngClass]="{
        'bg-success-50 border-success-300 text-success-600':
          toast.type === 'success',
        'bg-danger-50 border-danger-300 text-danger-600':
          toast.type === 'error',
        'bg-primary-50 border-primary-300 text-primary-600':
          toast.type === 'info',
        'bg-warning-50 border-warning-300 text-warning-600':
          toast.type === 'warning',
      }"
    >
      <div class="flex items-center overflow-hidden gap-2">
        <!-- Display the appropriate icon based on toast type -->
        <div class="flex items-center justify-center">
          <ng-icon
            [name]="
              toast.type === 'success'
                ? 'heroCheckCircle'
                : toast.type === 'error'
                  ? 'heroExclamationCircle'
                  : toast.type === 'warning'
                    ? 'heroExclamationTriangle'
                    : 'heroInformationCircle'
            "
            class="text-lg"
          />
        </div>
        <div class="w-full break-words" [innerHTML]="toast.message"></div>
      </div>
    </div>
  </div>
</div>
