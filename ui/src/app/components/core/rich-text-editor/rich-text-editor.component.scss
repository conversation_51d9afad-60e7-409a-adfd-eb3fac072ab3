:host ::ng-deep {
  .ProseMirror {
    table {
      border-collapse: collapse;
      table-layout: fixed;
      width: 100%;
      margin: 0;
      overflow: hidden;
    }
    
    table td, table th {
      min-width: 1em;
      border: 2px solid #ced4da;
      padding: 3px 5px;
      vertical-align: top;
      box-sizing: border-box;
      position: relative;
    }
    
    table th {
      font-weight: bold;
      background-color: #f1f3f5;
    }
    
    .selectedCell:after {
      z-index: 2;
      position: absolute;
      content: "";
      left: 0; right: 0; top: 0; bottom: 0;
      background: rgba(200, 200, 255, 0.4);
      pointer-events: none;
    }
    
    .column-resize-handle {
      position: absolute;
      right: -2px;
      top: 0;
      bottom: 0;
      width: 4px;
      background-color: #adf;
      pointer-events: none;
    }

    .tableWrapper {
      padding: 1em 0;
      overflow-x: auto;
    }
    
    .resize-cursor {
      cursor: col-resize;
    }
  }
}
