<div class="my-2">
  <label
    *ngIf="showLabel"
    [for]="elementId"
    class="block mb-2 text-sm font-medium text-secondary-500"
    >{{ elementName }}
    <span class="text-danger-500 text-xs" *ngIf="required">*</span>
  </label>
  <textarea
    #textarea
    [id]="elementId"
    [rows]="rows"
    (input)="onChange($event.target)"
    class="block w-full p-2.5 border border-secondary-200 text-secondary-900 text-sm rounded-lg focus-visible:ring-1 focus-visible:ring-primary-500 focus-visible:border-primary-500 disabled:bg-secondary-100 focus-visible:outline-none overflow-x-hidden overflow-y-auto"
    [placeholder]="elementPlaceHolder"
    [value]="value"
    [disabled]="isDisabled"
  ></textarea>
</div>
