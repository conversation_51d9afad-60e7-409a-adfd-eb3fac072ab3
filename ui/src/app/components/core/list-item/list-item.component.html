<div class="grid grid-cols-1 gap-5 sm:gap-6 lg:gap-6 mt-3">
  <div class="col-span-1 flex rounded-md shadow-sm">
    <div
      class="flex flex-1 items-center justify-between truncate rounded-lg border bg-white cursor-pointer hover:bg-secondary-50 transition-colors relative"
    >
      <div class="flex-1 truncate p-4 text-sm flex flex-col gap-2">
        <a class="font-semibold text-secondary-500 text-sm">
          {{ tag }}
          <span
            *ngIf="payload.pmoId"
            class="inline-flex items-center px-1.5 ml-1.5 text-xs bg-secondary-100 rounded-xl"
          >
            <img
              *ngIf="getPmoLogo()"
              [src]="getPmoLogo()"
              alt="PMO"
              class="w-3 h-3 mr-1"
            />
            {{ payload.pmoId }}
          </span>
        </a>
        <div class="flex flex-col gap-1">
          <h1 class="text-secondary-900 text-base font-medium truncate">
            {{ payload.name }}
          </h1>
          <ng-container *ngIf="payload.description as description">
            <app-rich-text-editor
              mode="view"
              [content]="description"
              editorClass="prose-secondary-view prose-xs"
            >
            </app-rich-text-editor>
          </ng-container>
        </div>
      </div>
      <div class="flex-shrink-0 pr-2 justify-between gap-x-6">
        <ng-content />
      </div>
    </div>
  </div>
</div>
