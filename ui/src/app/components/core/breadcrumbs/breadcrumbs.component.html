<nav
  class="flex [app-region:no-drag]"
  aria-label="Breadcrumb"
  *ngIf="breadcrumbs$ | async as breadcrumbs"
>
  <!--Back button-->
  <!-- <div class="flex mr-3">
    <button
      [disabled]="breadcrumbs.length < 1"
      (click)="navigateToPreviousPage()"
      class="inline-flex items-center text-xs font-normal text-secondary-300"
      [ngClass]="{
        'hover:text-white': breadcrumbs.length > 0,
        'text-secondary-500': breadcrumbs.length < 1,
      }"
    >
      <ng-icon
        class="text-base"
        name="heroArrowLeft"
        strokeWidth="2.5"
      ></ng-icon>
    </button>
  </div> -->
  <!--Solutions Breadcrumbs-->
  <ol class="inline-flex items-center space-x-1">
    <li class="inline-flex items-center">
      <a
        routerLink="/apps"
        (click)="navigateTo({ label: 'Solutions', url: '/apps' })"
        class="inline-flex items-center text-sm font-normal cursor-pointer"
        [class.font-semibold]="breadcrumbs.length == 0"
      >
        <ng-icon class="text-sm mr-1" name="heroHome" strokeWidth="2"></ng-icon>
        Solutions
      </a>
    </li>
    <li *ngFor="let breadcrumb of breadcrumbs; let last = last">
      <div class="flex items-center">
        <span class="text-secondary-300 text-xs">/</span>
        <span
          (click)="navigateTo(breadcrumb)"
          (keydown.enter)="navigateTo(breadcrumb)"
          (keydown.space)="navigateTo(breadcrumb); $event.preventDefault()"
          role="button"
          tabindex="0"
          [class.font-semibold]="last"
          [class.cursor-pointer]="breadcrumb.url"
          class="ml-1 text-sm cursor-pointer"
          [matTooltip]="breadcrumb.tooltipLabel || ''"
          >{{ breadcrumb.label }}</span
        >
      </div>
    </li>
  </ol>
</nav>
