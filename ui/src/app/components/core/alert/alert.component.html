<div
  *ngIf="isVisible"
  [@fadeInOut]
  class="fixed inset-0 z-50 flex items-center justify-center"
>
  <div class="fixed inset-0 bg-black opacity-50 z-40"></div>
  <div
    class="bg-white text-primary-600 px-4 py-4 rounded-md shadow-lg max-w-lg w-full z-50"
  >
    <div class="flex items-start">
      <ng-icon class="mt-1 mr-3 h-5 w-5" name="heroCheckCircleSolid"></ng-icon>
      <div class="w-full">
        <p class="font-bold">Success</p>
        <p class="mt-1 text-sm">
          {{ message }}
        </p>
        <div class="flex justify-end mt-4">
          <button
            class="rounded-md px-3 py-2 shadow-sm text-sm bg-primary-600 hover:bg-primary-500 text-white"
            (click)="closeAlert()"
          >
            <span class="font-medium text-md">Dismiss</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
