<div class="my-1.5">
  <label
    [for]="elementId"
    class="block mb-1.5 text-sm font-medium text-secondary-500"
    *ngIf="showLabel"
  >
    {{ elementName }}
    <span class="text-danger-500 text-xs" *ngIf="required">*</span>
  </label>
  <input
    [type]="elementType"
    [id]="elementId"
    (change)="onChange($event.target)"
    (keydown.enter)="onKeyDown()"
    [value]="value"
    [disabled]="isDisabled"
    class="block w-full p-2.5 border border-secondary-200 text-secondary-900 text-sm rounded-lg focus-visible:ring-1 focus-visible:ring-primary-500 focus-visible:border-primary-500 disabled:bg-secondary-100 focus-visible:outline-none {{
      customClass
    }}"
    [placeholder]="elementPlaceHolder"
  />
</div>
