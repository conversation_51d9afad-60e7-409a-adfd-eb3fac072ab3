<div class="flex flex-col gap-2 max-w-sm">
  <label
    *ngIf="label"
    class="block text-sm font-medium text-secondary-700 mb-2"
    >{{ label }}</label
  >
  <div class="flex items-center gap-2 w-full">
    <mat-slider
      class="flex-1"
      [min]="min"
      [max]="max"
      [step]="step"
      [disabled]="disabled"
    >
      <input
        matSliderThumb
        [value]="value"
        (valueChange)="onSliderChange($event)"
        (input)="onSliderChange($event)"
      />
    </mat-slider>
    <span class="text-sm font-medium text-muted-foreground whitespace-nowrap">{{
      value
    }}</span>
  </div>
</div>
