<form [formGroup]="adoForm" class="space-y-6" novalidate>
  <section class="space-y-4" aria-labelledby="ado-info">
    <div class="text-sm text-secondary-500">
      <p>{{ APP_MESSAGES.ADO_ACCORDION }}</p>
    </div>
    <div
      class="text-sm text-secondary-500 p-4 border border-gray-200 rounded-lg bg-gray-50"
    >
      <div class="mb-4 pb-3 border-b border-gray-300">
        <p class="text-sm text-gray-600 leading-relaxed">
          Map your work item types to
          {{ environment.ThemeConfiguration.appName }} requirements based on
          your project in ADO
        </p>
      </div>
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center flex-1">
            <span class="w-32 font-medium text-gray-700 flex-shrink-0"
              >Product Requirement <span class="text-red-500">*</span></span
            >
            <span class="mx-3 text-gray-400">→</span>
            <div class="flex-1 max-w-xs">
              <app-input-field
                formControlName="prdWorkItemType"
                elementId="prdWorkItemType"
                elementPlaceHolder="Work Item Type"
                [showLabel]="false"
                [required]="true"
                customClass="!p-2 !py-1.5 !text-sm !w-full !my-0 !border-gray-300"
              />
            </div>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center flex-1">
            <span class="w-32 font-medium text-gray-700 flex-shrink-0"
              >User Story <span class="text-red-500">*</span></span
            >
            <span class="mx-3 text-gray-400">→</span>
            <div class="flex-1 max-w-xs">
              <app-input-field
                formControlName="userStoryWorkItemType"
                elementId="userStoryWorkItemType"
                elementPlaceHolder="Work Item Type"
                [showLabel]="false"
                [required]="true"
                customClass="!p-2 !py-1.5 !text-sm !w-full !my-0 !border-gray-300"
              />
            </div>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center flex-1">
            <span class="w-32 font-medium text-gray-700 flex-shrink-0"
              >Task <span class="text-red-500">*</span></span
            >
            <span class="mx-3 text-gray-400">→</span>
            <div class="flex-1 max-w-xs">
              <app-input-field
                formControlName="taskWorkItemType"
                elementId="taskWorkItemType"
                elementPlaceHolder="Work Item Type"
                [showLabel]="false"
                [required]="true"
                customClass="!p-2 !py-1.5 !text-sm !w-full !my-0 !border-gray-300"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div>
      <app-input-field
        formControlName="organization"
        elementId="adoOrganization"
        elementName="Organization Name"
        elementPlaceHolder="Enter Azure DevOps Organization"
        [required]="true"
        aria-describedby="org-help"
      />
      <small id="org-help" class="sr-only">
        Enter your Azure DevOps organization name
      </small>
    </div>

    <div>
      <app-input-field
        formControlName="projectName"
        elementId="adoProjectName"
        elementName="Project Name"
        elementPlaceHolder="Enter Project Name"
        [required]="true"
        aria-describedby="project-help"
      />
      <small id="project-help" class="sr-only">
        Enter your Azure DevOps project name
      </small>
    </div>
  </div>

  <div>
    <app-input-field
      formControlName="personalAccessToken"
      elementId="adoPersonalAccessToken"
      elementName="Personal Access Token"
      elementPlaceHolder="Enter Personal Access Token"
      elementType="password"
      [required]="true"
      aria-describedby="token-help"
    />
    <small id="token-help" class="sr-only">
      Enter your Azure DevOps personal access token with appropriate permissions
    </small>
  </div>

  <div class="flex justify-end pt-4">
    <app-button
      [buttonContent]="getButtonText()"
      [theme]="isConnected() ? 'danger' : 'primary'"
      size="sm"
      (click)="isConnected() ? onDisconnect() : onConnect()"
      [disabled]="buttonDisabled()"
      [attr.aria-label]="
        isConnected()
          ? 'Disconnect from Azure DevOps'
          : 'Connect to Azure DevOps'
      "
    >
    </app-button>
  </div>
</form>
