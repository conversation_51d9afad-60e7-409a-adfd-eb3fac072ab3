<div
  *ngIf="documentList$ | async as projectList; else noProjectList"
  class="mx-auto max-w-6xl px-4 pb-4"
>
  <div class="py-4 sticky top-0 z-10 bg-white">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <h1 class="text-normal font-semibold text-secondary-700">
          {{ projectList.length > 0 ? getDescription(projectList[0].folderName) : '' }}
        </h1>
        <app-badge [badgeText]="projectList.length" />
      </div>
      <div class="flex gap-2">
        <app-button
          buttonContent="Add"
          icon="heroPlus"
          theme="secondary"
          size="sm"
          rounded="lg"
          (click)="projectList.length > 0 ? navigateToAdd(projectList[0].id, projectList[0].folderName) : navigateToAdd(selectedFolder.id, selectedFolder.title)"
        />
        <ng-container *ngIf="documentList$ | async as documentList">
          <app-export-dropdown
            [disabled]="projectList.length === 0"
            [groupedOptions]="getExportOptions(projectList[0].folderName)"
          />
        </ng-container>
      </div>
    </div>
    <app-search-input
      *ngIf="projectList.length > 0"
      placeholder="Search..."
      (searchChange)="onSearch($event)"
    ></app-search-input>
  </div>
  <ng-container *ngIf="filteredDocumentList$ | async as filteredProjectList">
    <div
      *ngIf="projectList.length > 0; else noDocuments"
      class="grid grid-cols-1 gap-3 sm:gap-4 lg:gap-4"
    >
      <ng-container
        *ngIf="filteredProjectList.length > 0; else noSearchResults"
      >
        <div
          *ngFor="let item of filteredProjectList"
          class="col-span-1 flex rounded-md shadow-sm relative"
        >
          <div
            class="flex flex-1 items-center justify-between truncate rounded-lg border bg-white hover:bg-secondary-50 transition-colors"
          >
            <div
              class="flex-1 truncate p-4 text-sm rounded-lg flex flex-col gap-2"
              (click)="navigateToEdit(item)"
              (keydown.enter)="navigateToEdit(item)"
              (keydown.space)="navigateToEdit(item); $event.preventDefault()"
              role="button"
              tabindex="0"
            >
              <a class="font-semibold text-secondary-500">
                {{ item.fileName.replace("-base.json", "") }}
                <span
                  *ngIf="item.content?.pmoId"
                  class="inline-flex items-center px-1.5 ml-1.5 text-xs bg-secondary-100 rounded-xl"
                >
                  <img
                    *ngIf="getPmoLogo()"
                    [src]="getPmoLogo()"
                    alt="PMO"
                    class="w-3 h-3 mr-1"
                  />
                  {{ item.content.pmoId }}
                </span>
              </a>
              <div class="flex flex-col gap-1">
                <h1
                  class="doc-section__item-title text-base font-medium truncate pr-[80px]"
                >
                  {{ item.content.title }}
                </h1>
                <ng-container *ngIf="item.content.requirement as requirement">
                  <app-rich-text-editor
                    editorClass="prose-secondary-view text-wrap overflow-y-hidden prose-xs"
                    mode="view"
                    [content]="item.formattedRequirement ?? ''"
                    [editable]="false"
                  >
                  </app-rich-text-editor>
                </ng-container>
              </div>
            </div>
            <div class="absolute top-4 right-4 flex space-x-2">
              <app-button
                *ngIf="item.folderName === requirementTypes.PRD"
                (click)="navigateToUserStories(item)"
                buttonContent="Stories"
                theme="secondary_outline"
                size="sm"
                rounded="lg"
              />
              <div
                *ngIf="item.folderName === 'BP'"
                (click)="navigateToBPFlow(item)"
                (keydown.enter)="navigateToBPFlow(item)"
                (keydown.space)="
                  navigateToBPFlow(item); $event.preventDefault()
                "
                role="button"
                tabindex="0"
                class="inline-flex h-8 w-8 items-center justify-center rounded-full bg-transparent bg-white text-secondary-400 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                <ng-icon class="text-xl" name="heroArrowsPointingOut"></ng-icon>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </ng-container>
</div>

<ng-template #noProjectList>
  <p class="text-center text-secondary-500">No project list available.</p>
</ng-template>

<ng-template #noDocuments>
  <p class="text-center text-secondary-500">No documents available.</p>
</ng-template>

<ng-template #noSearchResults>
  <p class="text-center text-secondary-500">No search results found.</p>
</ng-template>
