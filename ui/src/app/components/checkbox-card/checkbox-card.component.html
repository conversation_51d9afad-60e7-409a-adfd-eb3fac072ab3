<label class="flex flex-row gap-4 border border-secondary-200 hover:border-secondary-300 rounded-md px-4 py-3.5 w-full">
    <div class="grow">
        <ng-content></ng-content>
    </div>
    <div class="flex flex-col justify-center">
        <input type="checkbox" [value]="value" [checked]="_checked" (change)="handleOnChange($event)"
            [ngClass]="['w-4 h-4 text-primary-600 border-secondary-200 rounded focus:ring-primary-500 accent-primary-600']" />
    </div>
</label>