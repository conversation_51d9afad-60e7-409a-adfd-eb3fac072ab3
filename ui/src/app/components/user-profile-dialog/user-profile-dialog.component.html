<div class="fixed inset-0 flex items-center justify-center bg-secondary-800 bg-opacity-50">
  <div class="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
    <h2 class="text-xl font-semibold text-secondary-800 mb-4">
      We're updating!
    </h2>
    <p class="text-gray-600 mb-4">Please enter your username:</p>
    
    <div *ngIf="errorMessage" class="mb-4 p-2 bg-red-50 text-red-600 rounded">
      {{ errorMessage }}
    </div>
    
    <form (ngSubmit)="storeName()">
      <input
        type="username"
        class="border border-gray-300 rounded px-4 py-2 mb-4 w-full"
        [formControl]="userNameForm"
        placeholder="Enter your username"
      />
      
      <div *ngIf="userNameForm.touched && userNameForm.errors?.['required']" class="mb-2 text-red-600 text-sm">
        Username is required
      </div>
      
      <div *ngIf="userNameForm.touched && userNameForm.errors?.['username']" class="mb-2 text-red-600 text-sm">
        Please enter a valid username
      </div>
      
      <div class="flex justify-end space-x-4">
        <button
          type="submit"
          class="px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600"
        >
          Save
        </button>
      </div>
    </form>
  </div>
</div>