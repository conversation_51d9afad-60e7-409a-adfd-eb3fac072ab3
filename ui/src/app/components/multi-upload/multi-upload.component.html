<div class="mt-4 inline-flex flex-col gap-1.5">
  <input
    type="file"
    (change)="onFileSelected($event)"
    accept=".js,.ts,.tsx,.jsx.html,.css,.json,.xml,.py,.java,.c,.cpp,.cs,.php,.rb,.go,.swift"
    multiple
    #fileInput
    class="hidden"
  />
  <app-button
    buttonContent="Upload Code Files"
    theme="secondary_outline"
    size="sm"
    rounded="md"
    icon="heroDocument"
    (click)="triggerFileInput()"
  />
  <div
    *ngIf="files.length > 0"
    class="mt-2 w-full divide-y divide-secondary-200 border border-secondary-200 rounded-md text-xs font-medium"
  >
    <div class="p-2">
      <span *ngIf="files.length === 1">Selected File</span>
      <span *ngIf="files.length > 1">Selected Files</span>
    </div>
    <div *ngFor="let file of files" class="p-2 flex items-center gap-2">
      <ng-icon name="heroDocument" strokeWidth="2" class="w-4 h-4" />
      <span>{{ file }}</span>
    </div>
  </div>
</div>
