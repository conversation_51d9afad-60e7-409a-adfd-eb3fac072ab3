import { Component, Input } from '@angular/core';
import { Ng<PERSON>orO<PERSON>, NgIf } from '@angular/common';
import { McpServersListItemComponent } from '../mcp-servers-list-item/mcp-servers-list-item.component';
import { MCPServerDetails } from '../../../types/mcp.types';
import { CustomAccordionComponent } from '../../custom-accordion/custom-accordion.component';

@Component({
  selector: 'app-mcp-servers',
  templateUrl: './mcp-servers-list.component.html',
  standalone: true,
  imports: [NgForOf, NgIf, McpServersListItemComponent, CustomAccordionComponent]
})
export class McpServersListComponent {
  @Input() mcpServers: MCPServerDetails[] = [];
  @Input() isLoading = false;

  @Input() heading = "Model Context Protocol (MCP) Servers";
  @Input() subHeading = "View your connected MCP servers and their capabilities";
  @Input() showHeading = true;
}
