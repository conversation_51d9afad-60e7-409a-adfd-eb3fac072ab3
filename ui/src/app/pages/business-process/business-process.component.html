<div class="mx-auto max-w-6xl h-full">
  <div class="grid grid-cols-12 gap-4 h-full">
    <div
      class="bg-white border rounded-lg flex flex-col col-span-12 lg:col-span-7 h-[calc(100vh-80px)]"
    >
      <header
        class="flex justify-between items-center p-4 border-b border-secondary-200"
      >
        <h1 class="text-lg font-semibold" *ngIf="mode === 'add'">
          {{ folderName | expandDescription }}
        </h1>
        <h1 class="text-lg font-semibold" *ngIf="mode === 'edit'">
          {{ bpRequirementId }}
        </h1>
        <div class="flex items-center">
          <app-button
            *ngIf="mode === 'edit'"
            buttonContent="View BP Flow"
            theme="secondary"
            size="sm"
            rounded="md"
            (click)="navigateToBPFlow()"
          />
          <div
            *ngIf="mode === 'edit'"
            class="flex items-center space-x-1 text-3xl ml-3"
          >
            <app-button
              [isIconButton]="true"
              icon="heroTrash"
              theme="danger"
              size="sm"
              rounded="md"
              (click)="deleteBP()"
            ></app-button>
          </div>
        </div>
      </header>
      <form
        [formGroup]="businessProcessForm"
        class="flex flex-col h-full overflow-hidden"
      >
        <div class="p-4 flex flex-col gap-1.5 flex-1 overflow-y-auto">
          <div class="flex flex-col">
            <app-input-field
              elementPlaceHolder="Title"
              elementId="title"
              elementName="Title"
              formControlName="title"
              [required]="true"
            />
            <div
              *ngIf="
                businessProcessForm.get('title')?.errors?.['required'] &&
                businessProcessForm.get('title')?.touched
              "
              class="text-danger-500 text-sm"
            >
              Title is required
            </div>
          </div>

          <div class="flex flex-col gap-1.5">
            <label class="block text-sm font-medium text-secondary-500">
              Description
              <span class="text-red-500 text-xs">*</span>
            </label>
            <div class="flex flex-col gap-1.5">
              <app-rich-text-editor
                formControlName="content"
                [editorClass]="
                  mode === 'add'
                    ? 'h-[calc(100vh-600px)] min-h-24 overflow-y-auto'
                    : 'h-[calc(100vh-570px)] min-h-32 overflow-y-auto'
                "
              ></app-rich-text-editor>
              <ng-container *ngIf="businessProcessForm.get('content')?.touched">
                <span
                  *ngIf="
                    businessProcessForm.get('content')?.errors?.['required']
                  "
                  class="text-red-500 text-sm"
                >
                  Description is required
                </span>
              </ng-container>
            </div>
          </div>

          <div class="flex flex-col gap-3 mt-4">
            <div>
              <h2 class="text-sm font-medium text-secondary-900">Included BRDs</h2>
              <div class="mt-2 flex flex-wrap gap-1.5">
                <div *ngFor="let brd of selectedBRDs">
                  <app-pill
                    (clear)="removeBRD(brd)"
                    [showClear]="true"
                    variant="secondary"
                  >
                    {{ brd.fileName.split("-")[0] }}
                  </app-pill>
                </div>
                <button
                  (click)="
                    switchTab('includeFiles'); selectTab(requirementTypes.BRD)
                  "
                >
                  <app-pill variant="primary" contentContainerClass="select-none">
                    Add ->
                  </app-pill>
                </button>
              </div>
            </div>
            <div>
              <h2 class="text-sm font-medium text-secondary-900">Included PRDs</h2>
              <div class="mt-2 flex flex-wrap gap-1.5">
                <div *ngFor="let prd of selectedPRDs">
                  <app-pill
                    (clear)="removePRD(prd)"
                    [showClear]="true"
                    variant="secondary"
                  >
                    {{ prd.fileName.split("-")[0] }}
                  </app-pill>
                </div>
                <button
                  (click)="
                    switchTab('includeFiles'); selectTab(requirementTypes.PRD)
                  "
                >
                  <app-pill variant="primary" contentContainerClass="select-none">
                    Add ->
                  </app-pill>
                </button>
              </div>
            </div>
          </div>

          <!-- Display empty message if no documents are present -->
          <div
            *ngIf="selectedPRDs.length === 0 && selectedBRDs.length === 0"
            class="pb-4"
          >
            <div
              *ngIf="businessProcessForm.errors?.['noPrdOrBrd']"
              class="text-danger-500 text-center text-xs mt-1"
            >
              Please select at least one PRD or BRD.
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-2 p-4 border-t border-secondary-200">
          <app-button
            buttonContent="Enhance with AI"
            icon="heroSparklesSolid"
            theme="secondary_outline"
            size="sm"
            rounded="lg"
            (click)="
              mode === 'add'
                ? addBusinessProcess(true)
                : updateBusinessProcess(true)
            "
            [disabled]="checkFormValidity()"
          ></app-button>
          <app-button
            buttonContent="Update"
            theme="primary"
            size="sm"
            rounded="md"
            *ngIf="mode === 'edit'"
            (click)="updateBusinessProcess()"
            [disabled]="checkFormValidity()"
          />
          <app-button
            buttonContent="Add"
            theme="primary"
            size="sm"
            rounded="md"
            *ngIf="mode === 'add'"
            [disabled]="checkFormValidity()"
            (click)="addBusinessProcess()"
          />
        </div>
      </form>
    </div>

    <div class="bg-white border rounded-lg col-span-12 lg:col-span-5 h-[calc(100vh-80px)] flex flex-col">
      <div
        class="flex justify-center rounded-lg bg-white w-full border-b-[0.5px] border-secondary-300 rounded-b-none"
      >
        <div
          class="tabs w-full max-w-4xl overflow-hidden flex rounded-t-lg p-1.5 gap-0.5"
        >
          <!-- chat tab trigger -->
          <ng-container *ngIf="mode === 'edit'">
            <ng-container
              [ngTemplateOutlet]="TabTrigger"
              [ngTemplateOutletContext]="{
                label: 'Talk to HAI',
                isTabActive: activeTab === 'chat',
                tabId: 'chat',
                onClick: switchTab,
                icon: 'heroSparklesSolid'
              }"
            >
            </ng-container>
          </ng-container>
          <!-- include files tab trigger -->
          <ng-container
            [ngTemplateOutlet]="TabTrigger"
            [ngTemplateOutletContext]="{
              label: 'Include BRDs & PRDs',
              isTabActive: activeTab === 'includeFiles',
              tabId: 'includeFiles',
              onClick: switchTab,
            }"
          >
          </ng-container>

          <!-- trigger template -->
          <ng-template
            #TabTrigger
            let-label="label"
            let-isTabActive="isTabActive"
            let-tabId="tabId"
            let-onClick="onClick"
            let-icon="icon"
          >
            <button
              (click)="onClick(tabId)"
              class="flex-grow text-center py-2.5 text-sm font-medium rounded-lg border"
              [ngClass]="{
                'border-secondary-200 bg-primary-50 text-primary-600':
                  isTabActive,
                'text-secondary-500 border-transparent hover:bg-primary-50':
                  !isTabActive,
              }"
            >
              <span *ngIf="icon" class="flex items-center justify-center gap-2">
                <ng-icon [name]="icon" class="text-lg"></ng-icon>
                <span>{{ label }}</span>
              </span>
              <span *ngIf="!icon">{{ label }}</span>
            </button>
          </ng-template>
        </div>
      </div>

      <div *ngIf="activeTab === 'includeFiles'" class="flex-1 flex flex-col min-h-0">
        <div class="flex justify-center w-full px-3.5 pt-3.5 gap-0.5">
          <ng-container
            [ngTemplateOutlet]="TabTrigger"
            [ngTemplateOutletContext]="{
              label: 'PRD ( ' + selectedPRDs.length + ' )',
              isTabActive: selectedTab === requirementTypes.PRD,
              tabId: requirementTypes.PRD,
              onClick: selectTab,
            }"
          >
          </ng-container>
          <ng-container
            [ngTemplateOutlet]="TabTrigger"
            [ngTemplateOutletContext]="{
              label: 'BRD ( ' + selectedBRDs.length + ' )',
              isTabActive: selectedTab === requirementTypes.BRD,
              tabId: requirementTypes.BRD,
              onClick: selectTab,
            }"
          >
          </ng-container>
        </div>
        <div class="flex-1 min-h-0 pb-4">
          <div class="h-full" *ngIf="originalDocumentList$ | async as list">
            <div
              *ngIf="!!list?.length; else noDocuments"
              class="flex flex-col gap-4 w-full overflow-y-auto h-full px-4 pt-4"
            >
              <div *ngFor="let item of list" class="flex w-full">
                <ng-container
                  *ngIf="
                    (selectedTab === requirementTypes.PRD &&
                      item.folderName === requirementTypes.PRD) ||
                    (selectedTab === requirementTypes.BRD &&
                      item.folderName === requirementTypes.BRD)
                  "
                >
                  <app-checkbox-card
                    [checked]="
                      isSelected(
                        {
                          requirement: item?.content?.requirement,
                          fileName: item.fileName,
                        },
                        selectedTab
                      )
                    "
                    [value]="
                      JSON.stringify({
                        requirement: item.content.requirement,
                        fileName: item.fileName,
                      })
                    "
                    (onCheckedChange)="
                      toggleSelection(
                        $event,
                        {
                          requirement: item.content.requirement || '',
                          fileName: item.fileName,
                        },
                        selectedTab
                      )
                    "
                    class="w-full"
                  >
                    <div class="text-sm flex flex-col gap-1">
                      <a class="font-semibold text-secondary-500">
                        {{
                          (item.fileName || "").replace("-base.json", "")
                        }}
                      </a>
                      <h1 class="text-base font-medium line-clamp-1">
                        {{ item.content.title }}
                      </h1>
                      <app-rich-text-editor
                        editorClass="prose-secondary-view text-wrap overflow-y-hidden prose-xs"
                        mode="view"
                        [content]="
                          truncatePRDandBRDRequirement(
                            item.content.requirement || '',
                            item.folderName || ''
                          )
                        "
                        [editable]="false"
                      >
                      </app-rich-text-editor>
                    </div>
                  </app-checkbox-card>
                </ng-container>
              </div>
            </div>
          </div>
          <ng-template #noDocuments>
            <div class="flex items-center justify-center h-full">
              <h2 class="text-center text-secondary-500">
                No documents available
              </h2>
            </div>
          </ng-template>
        </div>
      </div>
      <div *ngIf="activeTab === 'chat'" class="flex-1 min-h-0">
        <app-chat
          class="h-full block"
          chatType="requirement"
          [name]="name"
          [description]="description"
          [fileName]="fileName"
          [chatHistory]="chatHistory"
          [supportsAddFromCode]="false"
          [baseContent]="businessProcessForm.getRawValue().content"
          (getContent)="appendRequirement($event)"
          (updateChatHistory)="updateChatHistory($event)"
          [containerClass]="'border-none h-full rounded-b-lg'"
        />
      </div>
    </div>
  </div>
</div>
