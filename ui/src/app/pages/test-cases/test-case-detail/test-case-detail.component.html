<div class="bg-white border rounded-lg shadow-md flex flex-col h-full w-full overflow-hidden test-case-detail-container">
  <div class="p-6">
    <div class="flex justify-between items-center">
      <h1 class="text-2xl font-semibold">
        {{ mode === 'add' ? 'Add New Test Case' : (mode === 'edit' ? 'Edit Test Case ' + (testCaseForm.get('id')?.value || '') : (testCaseForm.get('id')?.value || 'Test Case')) }}
      </h1>
      <div class="flex gap-4">
        <ng-container *ngIf="mode === 'view'">
          <app-button buttonContent="Edit" icon="heroPencil" theme="primary" size="sm" rounded="lg" (click)="enableEditMode()" />
          <app-button buttonContent="Delete" icon="heroTrash" theme="danger_outline" size="sm" rounded="lg" (click)="deleteTestCase()" matTooltip="Delete this test case" />
          <app-button buttonContent="Back" icon="heroArrowLeft" theme="primary_outline" size="sm" rounded="lg" (click)="cancel()" />
        </ng-container>
        <ng-container *ngIf="mode === 'edit' || mode === 'add'">
          <app-button [buttonContent]="mode === 'add' ? 'Create' : 'Save'" icon="heroCheck" theme="primary" size="sm" rounded="lg" (click)="saveTestCase()" />
          <app-button buttonContent="Cancel" icon="heroXMark" theme="secondary" size="sm" rounded="lg" (click)="cancel()" />
        </ng-container>
      </div>
    </div>
  </div>

  <div class="px-8 pb-10 overflow-y-auto" [formGroup]="testCaseForm">
    <ng-container *ngIf="mode === 'view'; else editForm">
      <div class="space-y-10">
        <div class="bg-white border border-secondary-200 rounded-xl p-8 shadow-sm">
          <h2 class="text-lg font-semibold text-secondary-800 mb-6 flex items-center">
            <ng-icon name="heroInformationCircle" class="mr-3 text-primary-500 w-5 h-5"></ng-icon>
            Test Case Details
          </h2>
          <dl class="space-y-6">
            <div>
              <dt class="text-sm font-medium text-secondary-600 mb-1">Title</dt>
              <dd class="text-base text-secondary-800">{{ testCaseForm.get('title')?.value || '—' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-secondary-600 mb-1">Description</dt>
              <dd class="text-base text-secondary-800 whitespace-pre-line">{{ testCaseForm.get('description')?.value || '—' }}</dd>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-x-6">
              <div>
                <dt class="text-sm font-medium text-secondary-600 mb-1">Type</dt>
                <dd><span class="inline-block px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-700">{{ testCaseForm.get('type')?.value || '—' }}</span></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-secondary-600 mb-1">Status</dt>
                <dd><span class="inline-block px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-700">{{ testCaseForm.get('status')?.value || '—' }}</span></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-secondary-600 mb-1">Priority</dt>
                <dd><span class="inline-block px-3 py-1 text-sm font-semibold rounded-full bg-primary-100 text-primary-700">{{ testCaseForm.get('priority')?.value || '—' }}</span></dd>
              </div>
            </div>
          </dl>
        </div>

        <div class="bg-white border border-secondary-200 rounded-xl p-8 shadow-sm">
          <h2 class="text-lg font-semibold text-secondary-800 mb-4 flex items-center">
            <ng-icon name="heroClipboard" class="mr-3 text-primary-500 w-5 h-5"></ng-icon>
            Pre-conditions
          </h2>
          <ng-container *ngIf="testCaseForm.get('preConditions')?.value?.length; else noPreConditions">
            <ul class="list-disc pl-8 text-secondary-800 space-y-2 text-base">
              <li *ngFor="let item of testCaseForm.get('preConditions')?.value">{{ item }}</li>
            </ul>
          </ng-container>
          <ng-template #noPreConditions>
            <p class="text-secondary-500 italic text-base pl-8">No pre-conditions specified.</p>
          </ng-template>
        </div>

        <div class="bg-white border border-secondary-200 rounded-xl p-8 shadow-sm">
          <h2 class="text-lg font-semibold text-secondary-800 mb-4 flex items-center">
            <ng-icon name="heroListBullet" class="mr-3 text-primary-500 w-5 h-5"></ng-icon>
            Test Steps
          </h2>
          <div class="space-y-6">
            <div *ngFor="let step of testCaseForm.get('steps')?.value; let i = index" class="bg-secondary-50 border border-secondary-100 rounded-lg p-6">
              <h3 class="text-sm font-semibold text-secondary-700 mb-3">Step {{ i + 1 }}</h3>
              <p class="text-sm text-secondary-700 mb-2"><strong>Action:</strong> {{ step.action }}</p>
              <p class="text-sm text-secondary-700"><strong>Expected Result:</strong> {{ step.expectedResult }}</p>
            </div>
          </div>
        </div>


        <div class="bg-white border border-secondary-200 rounded-xl p-8 shadow-sm">
          <h2 class="text-lg font-semibold text-secondary-800 mb-4 flex items-center">
            <ng-icon name="heroClipboardDocument" class="mr-3 text-primary-500 w-5 h-5"></ng-icon>
            Post-conditions
          </h2>
          <ng-container *ngIf="testCaseForm.get('postConditions')?.value?.length; else noPostConditions">
            <ul class="list-disc pl-8 text-secondary-800 space-y-2 text-base">
              <li *ngFor="let item of testCaseForm.get('postConditions')?.value">{{ item }}</li>
            </ul>
          </ng-container>
          <ng-template #noPostConditions>
            <p class="text-secondary-500 italic text-base pl-8">No post-conditions specified.</p>
          </ng-template>
        </div>
      </div>
    </ng-container>

    <ng-template #editForm>
      <div class="space-y-8">
        <div class="mb-6">
          <h2 class="text-lg font-semibold text-secondary-800 mb-4 flex items-center">
            <ng-icon name="heroInformationCircle" class="mr-3 text-primary-500 w-5 h-5"></ng-icon>
            Test Case Details
          </h2>
        </div>
        <div class="space-y-6">
          <div>
            <div class="flex items-center mb-1">
              <span class="text-sm font-medium text-secondary-600">Title</span>
              <span class="text-danger-500 ml-1">*</span>
            </div>
            <app-input-field elementId="title" formControlName="title" [elementPlaceHolder]="'Enter test case title'" [hasError]="hasError('title')" errorMessage="Title is required" />
          </div>
          <div>
            <div class="flex items-center mb-1">
              <span class="text-sm font-medium text-secondary-600">Description</span>
              <span class="text-danger-500 ml-1">*</span>
            </div>
            <app-textarea-field formControlName="description" [elementPlaceHolder]="'Enter description'" [rows]="3" [hasError]="hasError('description')" errorMessage="Description is required" />
          </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <div class="flex items-center mb-1">
              <span class="text-sm font-medium text-secondary-600">Type</span>
              <span class="text-danger-500 ml-1">*</span>
            </div>
            <app-select [options]="typeOptions" placeholder="Select type" formControlName="type" [required]="true"></app-select>
          </div>
          <div>
            <div class="flex items-center mb-1">
              <span class="text-sm font-medium text-secondary-600">Status</span>
              <span class="text-danger-500 ml-1">*</span>
            </div>
            <app-select [options]="statusOptions" placeholder="Select status" formControlName="status" [required]="true"></app-select>
          </div>
          <div>
            <div class="flex items-center mb-1">
              <span class="text-sm font-medium text-secondary-600">Priority</span>
              <span class="text-danger-500 ml-1">*</span>
            </div>
            <app-select [options]="priorityOptions" placeholder="Select priority" formControlName="priority" [required]="true"></app-select>
          </div>
        </div>

        <div class="mt-10">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-secondary-800 flex items-center">
              <ng-icon name="heroClipboard" class="mr-3 text-primary-500 w-5 h-5"></ng-icon>
              Pre-conditions
            </h2>
            <app-button buttonContent="Add Pre-condition" icon="heroPlus" theme="secondary_outline" size="sm" rounded="lg" (click)="addPreCondition()" />
          </div>
          <div formArrayName="preConditions" class="space-y-3">
            <div *ngFor="let condition of preConditions.controls; let i = index" class="flex items-center gap-3">
              <app-input-field [formControlName]="i" [elementPlaceHolder]="'Enter pre-condition'" class="flex-grow" />
              <app-button [isIconButton]="true" icon="heroTrash" theme="danger_outline" size="xs" rounded="lg" (click)="removePreCondition(i)" />
            </div>
            <p *ngIf="preConditions.controls.length === 0" class="text-secondary-500 italic text-base">No pre-conditions specified. Click "Add Pre-condition" to add one.</p>
          </div>
        </div>

        <div class="mt-10">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-secondary-800 flex items-center">
              <ng-icon name="heroListBullet" class="mr-3 text-primary-500 w-5 h-5"></ng-icon>
              Test Steps <span class="text-danger-500 ml-1">*</span> 
            </h2>
            <app-button buttonContent="Add Step" icon="heroPlus" theme="primary" size="sm" rounded="lg" (click)="addStep()" />
          </div>
          <div formArrayName="steps" class="space-y-6">
            <p *ngIf="steps.controls.length === 0" class="text-secondary-500 italic text-base">No test steps specified. Click "Add Step" to add one.</p>
            <div *ngFor="let step of steps.controls; let i = index" [formGroupName]="i" class="border border-secondary-200 rounded-lg p-6">
              <div class="flex flex-col md:flex-row md:items-center gap-6">
                <div class="flex-1">
                  <div class="flex items-center mb-1">
                    <span class="text-sm font-medium text-secondary-600">Action</span>
                    <span class="text-danger-500 ml-1">*</span>
                  </div>
                  <app-textarea-field formControlName="action" [elementPlaceHolder]="'Describe action'" [rows]="3" [hasError]="hasStepError(i, 'action')" errorMessage="Action is required" />
                </div>
                <div class="flex-1">
                  <div class="flex items-center mb-1">
                    <span class="text-sm font-medium text-secondary-600">Expected Result</span>
                    <span class="text-danger-500 ml-1">*</span>
                  </div>
                  <app-textarea-field formControlName="expectedResult" [elementPlaceHolder]="'Describe expected result'" [rows]="3" [hasError]="hasStepError(i, 'expectedResult')" errorMessage="Expected result is required" />
                </div>
                <div class="flex-shrink-0">
                  <app-button [isIconButton]="true" icon="heroTrash" theme="danger_outline" size="xs" rounded="lg" (click)="removeStep(i)" />
                </div>
              </div>
            </div>
          </div>
        </div>


        <div class="mt-10">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-secondary-800 flex items-center">
              <ng-icon name="heroClipboardDocument" class="mr-3 text-primary-500 w-5 h-5"></ng-icon>
              Post-conditions
            </h2>
            <app-button buttonContent="Add Post-condition" icon="heroPlus" theme="secondary_outline" size="sm" rounded="lg" (click)="addPostCondition()" />
          </div>
          <div formArrayName="postConditions" class="space-y-3">
            <div *ngFor="let condition of postConditions.controls; let i = index" class="flex items-center gap-3">
              <app-input-field [formControlName]="i" [elementPlaceHolder]="'Enter post-condition'" class="flex-grow" />
              <app-button [isIconButton]="true" icon="heroTrash" theme="danger_outline" size="xs" rounded="lg" (click)="removePostCondition(i)" />
            </div>
            <p *ngIf="postConditions.controls.length === 0" class="text-secondary-500 italic text-base">No post-conditions specified. Click "Add Post-condition" to add one.</p>
          </div>
        </div>
      </div>
    </ng-template>
  </div>

</div>
