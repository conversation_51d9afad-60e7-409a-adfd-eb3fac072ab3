<ngx-loading
  [show]="loading"
  [config]="{ backdropBorderRadius: '3px' }"
></ngx-loading>
<header
  class="flex items-center justify-between py-3 bg-secondary-50 sticky top-0"
>
  <h1 class="text-lg font-semibold">Create Solution</h1>
  <div class="flex gap-2">
    <app-button
      buttonContent="Cancel"
      [disabled]="loading"
      size="sm"
      theme="secondary_outline"
      (click)="navigateToDashboard()"
    ></app-button>
    <app-button
      buttonContent="Create Solution"
      [disabled]="isCreateSolutionDisabled"
      size="sm"
      (click)="createSolution()"
    ></app-button>
  </div>
</header>
<form [formGroup]="solutionForm" class="flex flex-col gap-3 overflow-auto pb-4">
  <!-- Basic Information Section -->
  <app-custom-accordion [id]="'basic-info'" [isOpen]="true">
    <ng-container accordion-trigger>
      <div class="flex flex-col text-sm gap-0.5">
        <span class="font-semibold text-foreground">Basic Information</span>
        <span class="font-normal text-muted-foreground"
          >Provide essential details about the application you intend to
          develop.</span
        >
      </div>
    </ng-container>
    <ng-container accordion-body>
      <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-3">
        <label
          class="block text-sm font-medium leading-6 text-secondary-900 sm:pt-1.5"
          >Solution Name<span class="text-danger-500 text-xs"> *</span></label
        >

        <app-input-field
          formControlName="name"
          elementId="solution-name"
          elementName="Solution Name"
          elementPlaceHolder="Solution Name"
          [showLabel]="false"
          [required]="true"
          class="sm:col-span-2"
        />
        <app-error-message [errorControl]="solutionForm.get('name')" />
      </div>

      <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-3">
        <div>
          <label
            class="block text-sm font-medium leading-6 text-secondary-900 sm:pt-1.5"
            >Solution Description<span class="text-danger-500 text-xs">
              *</span
            ></label
          >
          <p class="text-xs leading-6 text-muted-foreground">
            Write a few sentences about your idea
          </p>
        </div>
        <div class="mt-2 sm:col-span-2 sm:mt-0">
          <app-textarea-field
            formControlName="description"
            elementId="solution-description"
            elementName="Solution Description"
            elementPlaceHolder="Solution Description"
            [showLabel]="false"
            [rows]="4"
            [required]="true"
          />
          <app-error-message [errorControl]="solutionForm.get('description')" />
        </div>
      </div>

      <div class="sm:grid sm:grid-cols-3 sm:items-start sm:gap-4 sm:py-3">
        <div>
          <label
            class="block text-sm font-medium leading-6 text-secondary-900 sm:pt-1.5"
            >Technical Details<span class="text-danger-500 text-xs">
              *</span
            ></label
          >
          <p class="text-xs leading-6 text-muted-foreground">
            Few sentences about the technical stack of your app
          </p>
        </div>
        <div class="mt-2 sm:col-span-2 sm:mt-0">
          <app-textarea-field
            formControlName="technicalDetails"
            elementId="technical-details"
            elementName="Technical Details"
            elementPlaceHolder="Technical Details"
            [showLabel]="false"
            [rows]="4"
            [required]="true"
          />
          <app-error-message
            [errorControl]="solutionForm.get('technicalDetails')"
          />
        </div>
      </div>

      <div class="sm:grid sm:grid-cols-3 sm:items-center sm:gap-4 sm:py-3">
        <div>
          <label class="block text-sm font-medium leading-6 text-secondary-900"
            >Is solution built already?<span class="text-danger-500 text-xs">
              *</span
            ></label
          >
          <p class="text-xs leading-6 text-muted-foreground">
            {{ getSolutionToggleDescription() }}
          </p>
        </div>
        <div class="col-span-2 flex items-center">
          <div class="mt-1.5">
            <app-toggle
              [isActive]="solutionForm.get('cleanSolution')?.value"
              (toggleChange)="
                solutionForm.get('cleanSolution')?.setValue($event)
              "
              isPlainToggle="true"
            ></app-toggle>
          </div>
        </div>
      </div>
    </ng-container>
  </app-custom-accordion>

  <!-- Preferences Section -->
  <app-custom-accordion
    [id]="'preferences'"
    *ngIf="showGenerationPreferencesTab()"
    [isOpen]="false"
  >
    <ng-container accordion-trigger>
      <div class="flex flex-col text-sm gap-0.5">
        <span class="font-semibold text-foreground">Preferences</span>
        <span class="font-normal text-muted-foreground"
          >Configure and optimize your project settings to align with your
          requirements.</span
        >
      </div>
    </ng-container>
    <ng-container accordion-body>
      <div
        class="sm:grid sm:grid-cols-3 sm:items-center sm:gap-4 sm:py-4"
        *ngIf="!solutionForm.get('cleanSolution')?.value"
      >
        <div>
          <label class="block text-sm font-medium leading-6 text-secondary-900">
            Business Requirements (BRD)<span class="text-danger-500 text-xs">
              *</span
            >
          </label>
          <p class="text-xs leading-6 text-muted-foreground">
            Set the minimum number of Business Requirements
          </p>
        </div>
        <div class="flex items-center col-span-2 gap-4">
          <app-toggle
            [isActive]="solutionForm.get('BRD')?.get('enabled')?.value"
            (toggleChange)="onRequirementToggle('BRD', $event)"
            isPlainToggle="true"
          ></app-toggle>
          <div
            formGroupName="BRD"
            *ngIf="solutionForm.get('BRD')?.get('enabled')?.value"
          >
            <app-slider formControlName="minCount"></app-slider>
          </div>
        </div>
      </div>

      <div
        class="sm:grid sm:grid-cols-3 sm:items-center sm:gap-4 sm:py-4"
        *ngIf="!solutionForm.get('cleanSolution')?.value"
      >
        <div>
          <label class="block text-sm font-medium leading-6 text-secondary-900">
            Product Requirements (PRD)<span class="text-danger-500 text-xs">
              *</span
            >
          </label>
          <p class="text-xs leading-6 text-muted-foreground">
            Set the minimum number of Product Requirements
          </p>
        </div>
        <div class="flex items-center col-span-2 gap-4">
          <app-toggle
            [isActive]="solutionForm.get('PRD')?.get('enabled')?.value"
            (toggleChange)="onRequirementToggle('PRD', $event)"
            isPlainToggle="true"
          ></app-toggle>
          <div
            *ngIf="solutionForm.get('PRD')?.get('enabled')?.value"
            formGroupName="PRD"
          >
            <app-slider formControlName="minCount"></app-slider>
          </div>
        </div>
      </div>

      <div
        class="sm:grid sm:grid-cols-3 sm:items-center sm:gap-4 sm:py-4"
        *ngIf="!solutionForm.get('cleanSolution')?.value"
      >
        <div>
          <label class="block text-sm font-medium leading-6 text-secondary-900">
            UI Requirements (UIR)<span class="text-danger-500 text-xs"> *</span>
          </label>
          <p class="text-xs leading-6 text-muted-foreground">
            Set the minimum number of UI Requirements
          </p>
        </div>
        <div class="flex items-center col-span-2 gap-4">
          <app-toggle
            [isActive]="solutionForm.get('UIR')?.get('enabled')?.value"
            (toggleChange)="onRequirementToggle('UIR', $event)"
            isPlainToggle="true"
          ></app-toggle>
          <div
            *ngIf="solutionForm.get('UIR')?.get('enabled')?.value"
            formGroupName="UIR"
          >
            <app-slider formControlName="minCount"></app-slider>
          </div>
        </div>
      </div>

      <div
        class="sm:grid sm:grid-cols-3 sm:items-center sm:gap-4 sm:py-4"
        *ngIf="!solutionForm.get('cleanSolution')?.value"
      >
        <div>
          <label class="block text-sm font-medium leading-6 text-secondary-900">
            Non-Functional Requirements (NFR)<span
              class="text-danger-500 text-xs"
            >
              *</span
            >
          </label>
          <p class="text-xs leading-6 text-muted-foreground">
            Set the minimum number of Non-Functional Requirements
          </p>
        </div>
        <div class="flex items-center col-span-2 gap-4">
          <app-toggle
            [isActive]="solutionForm.get('NFR')?.get('enabled')?.value"
            (toggleChange)="onRequirementToggle('NFR', $event)"
            isPlainToggle="true"
          ></app-toggle>
          <div
            *ngIf="solutionForm.get('NFR')?.get('enabled')?.value"
            formGroupName="NFR"
          >
            <app-slider formControlName="minCount"></app-slider>
          </div>
        </div>
      </div>
    </ng-container>
  </app-custom-accordion>

  <!-- MCP Integrations Section -->
  <app-custom-accordion [id]="'integrations'">
    <ng-container accordion-trigger>
      <div class="flex flex-col text-sm gap-0.5">
        <span class="font-semibold text-foreground">MCP Integrations</span>
        <span class="font-normal text-muted-foreground"
          >Integrate Model Context Protocol servers with your project.</span
        >
      </div>
    </ng-container>
    <ng-container accordion-body>
      <div class="flex flex-col gap-4 w-full">
        <app-mcp-integration-configurator
          formControlName="mcpSettings"
        ></app-mcp-integration-configurator>
      </div>
    </ng-container>
  </app-custom-accordion>
</form>
