<div class="mx-auto max-w-6xl h-full">
  <div
    [ngClass]="[
      'grid gap-4 items-stretch',
      mode === 'edit' || isPRD()
        ? 'lg:grid-cols-[repeat(3,minmax(0,1fr))] grid-cols-1'
        : 'grid-cols-1',
    ]"
  >
    <div
      class="flex flex-col h-[calc(100vh-80px)] bg-white border border-secondary-200 rounded-lg lg:col-span-2"
    >
      <header
        class="flex justify-between items-center p-4 border-b border-secondary-200"
      >
        <h1 class="text-normal font-semibold">
          {{
            mode === "edit"
              ? fileName.split("-")[0]
              : getDescription(folderName)
          }}
        </h1>
        <div *ngIf="mode === 'edit'" class="flex gap-2">
          <app-button
            *ngIf="isPRD()"
            buttonContent="User Stories"
            icon="heroDocumentText"
            theme="secondary_outline"
            size="sm"
            rounded="md"
            (click)="navigateToUserStories()"
            matTooltip="View User Stories"
          ></app-button>
          <app-button
            [isIconButton]="true"
            icon="heroTrash"
            theme="danger"
            size="sm"
            rounded="md"
            (click)="deleteFile()"
            matTooltip="Delete"
          ></app-button>
        </div>
      </header>

      <form
        [formGroup]="requirementForm"
        (ngSubmit)="mode === 'edit' ? updateRequirement() : addRequirement()"
        class="flex flex-col h-full overflow-hidden"
      >
        <div class="p-4 flex flex-col gap-1.5 flex-1 overflow-y-auto">
          <div class="flex flex-col">
            <app-input-field
              [required]="true"
              elementPlaceHolder="Title"
              elementId="title"
              elementName="Title"
              formControlName="title"
            />
            <ng-container *ngIf="requirementForm.get('title')?.touched">
              <span
                *ngIf="requirementForm.get('title')?.errors?.['required']"
                class="text-red-500 text-sm"
              >
                Title is required
              </span>
            </ng-container>
          </div>

          <div class="flex flex-col gap-1.5">
            <label
              for="elementId"
              class="block text-sm font-medium text-secondary-500"
            >
              Description
              <span class="text-red-500 text-xs">*</span>
            </label>
            <div class="flex flex-col gap-1.5">
              <app-rich-text-editor
                #editor
                formControlName="content"
                editorClass="h-[calc(100vh-460px)] min-h-24 overflow-y-auto"
                [editable]="mode === 'edit'"
                appInlineEdit
                [editorInstance]="editor?.editor"
                [contextProvider]="getContentContext.bind(this)"
                [onContentUpdated]="handleInlineEditUpdate.bind(this)"
                (change)="onEditorReady(editor)"
              ></app-rich-text-editor>
              <ng-container *ngIf="requirementForm.get('content')?.touched">
                <span
                  *ngIf="requirementForm.get('content')?.errors?.['required']"
                  class="text-red-500 text-sm"
                >
                  Description is required
                </span>
              </ng-container>
            </div>
          </div>

          <app-multi-upload
            *ngIf="mode === 'add'"
            (fileContent)="handleFileContent($event)"
          ></app-multi-upload>

          <!-- Selected BRDs Section -->
          <div
            *ngIf="
              isPRD() && requirementForm.get('linkedBRDIds')?.value?.length > 0
            "
            class="flex flex-col gap-1.5 mt-4"
          >
            <h2 class="text-sm font-medium text-secondary-500">Included BRDs</h2>
            <div class="flex flex-wrap gap-1.5">
              <div
                *ngFor="let brdId of requirementForm.get('linkedBRDIds')?.value"
              >
                <app-pill
                  (clear)="removeLinkedBRDForPRD(brdId)"
                  [showClear]="true"
                >
                  {{ brdId }}
                </app-pill>
              </div>
            </div>
          </div>

          <!-- Linked PRDs Section -->
          <div
            *ngIf="
              isBRD() && requirementForm.get('linkedToPRDIds')?.value?.length > 0
            "
            class="flex flex-col gap-1.5 mt-4"
          >
            <h2 class="text-sm font-medium text-secondary-900">Linked PRDs</h2>
            <div class="flex flex-wrap gap-1.5">
              <div
                *ngFor="let prdId of requirementForm.get('linkedToPRDIds')?.value"
              >
                <app-pill
                  (clear)="removeLinkedPRDFromBRD(prdId)"
                  [showClear]="true"
                >
                  {{ prdId }}
                </app-pill>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-2 p-4 border-t border-secondary-200">
          <app-button
            buttonContent="Enhance with AI"
            icon="heroSparklesSolid"
            theme="secondary_outline"
            size="sm"
            rounded="lg"
            (click)="enhanceRequirementWithAI()"
            [disabled]="requirementForm.invalid"
          ></app-button>
          <app-button
            buttonContent="Update"
            theme="primary"
            size="sm"
            rounded="md"
            *ngIf="mode === 'edit'"
            type="submit"
            [disabled]="requirementForm.invalid"
          />
          <app-button
            buttonContent="Add"
            theme="primary"
            size="sm"
            rounded="md"
            *ngIf="mode === 'add'"
            type="submit"
            [disabled]="requirementForm.invalid"
          />
        </div>
      </form>
    </div>

    <!-- chat/include brds section for prd -->
    <div
      *ngIf="isPRD()"
      class="flex flex-col bg-white border rounded lg:col-span-1 h-[calc(100vh-80px)]"
    >
      <div
        class="flex justify-center rounded-lg bg-white w-full border-b-[0.5px] border-secondary-300 rounded-b-none"
      >
        <div
          class="tabs w-full max-w-4xl overflow-hidden flex rounded-t-lg p-1.5 gap-0.5"
        >
          <!-- chat tab trigger -->
          <ng-container *ngIf="mode === 'edit'">
            <ng-container
              [ngTemplateOutlet]="TabTrigger"
              [ngTemplateOutletContext]="{
                label: 'Talk to HAI',
                isTabActive: activeTab === 'chat',
                tabId: 'chat',
                icon: 'heroSparklesSolid'
              }"
            ></ng-container>
          </ng-container>
          <!-- include brd files tab trigger -->
          <ng-container
            [ngTemplateOutlet]="TabTrigger"
            [ngTemplateOutletContext]="{
              label: 'Include BRDs',
              isTabActive: activeTab === 'includeFiles',
              tabId: 'includeFiles',
            }"
          ></ng-container>

          <!-- trigger template -->
          <ng-template
            #TabTrigger
            let-label="label"
            let-isTabActive="isTabActive"
            let-tabId="tabId"
            let-icon="icon"
          >
            <button
              (click)="activeTab = tabId"
              class="flex-grow text-center py-2.5 text-sm font-medium rounded-lg border"
              [ngClass]="{
                'border-secondary-200 bg-primary-50 text-primary-600':
                  isTabActive,
                'text-secondary-500 border-transparent hover:bg-primary-50':
                  !isTabActive,
              }"
            >
              <span *ngIf="icon" class="flex items-center justify-center gap-2">
                <ng-icon [name]="icon" class="text-lg"></ng-icon>
                <span>{{ label }}</span>
              </span>
              <span *ngIf="!icon">{{ label }}</span>
            </button>
          </ng-template>
        </div>
      </div>

      <ng-container *ngIf="activeTab === 'includeFiles'">
        <ng-container *ngIf="documentList.length > 0; else noDocuments">
          <div
            class="grow flex flex-col gap-4 w-full p-4 overflow-y-auto h-full"
          >
            <div *ngFor="let item of documentList" class="flex w-full">
              <app-checkbox-card
                [checked]="
                  requirementForm
                    .get('linkedBRDIds')
                    ?.value?.includes(item.fileName.split('-')[0])
                "
                (onCheckedChange)="
                  handleLinkedBRDSelectionForPRD(
                    item.fileName.split('-')[0],
                    $event
                  )
                "
                class="w-full"
              >
                <div class="text-sm flex flex-col gap-1">
                  <a class="font-semibold text-secondary-500">
                    {{ item.fileName.split("-")[0] }}
                  </a>
                  <h1 class="text-base font-medium line-clamp-1">
                    {{ item.content.title }}
                  </h1>
                  <app-rich-text-editor
                    editorClass="prose-secondary-view text-wrap overflow-y-hidden prose-xs"
                    mode="view"
                    [content]="
                      truncateRequirementContent(item.content.requirement ?? '')
                    "
                    [editable]="false"
                  >
                  </app-rich-text-editor>
                </div>
              </app-checkbox-card>
            </div>
          </div>
        </ng-container>
        <ng-template #noDocuments>
          <div class="py-10 doc-section-height">
            <h2 class="text-center text-secondary-500">No BRDs available</h2>
          </div>
        </ng-template>
      </ng-container>

      <div *ngIf="activeTab === 'chat'" class="flex-1 min-h-0">
        <app-chat
          class="h-full block"
          chatType="requirement"
          [name]="name"
          [description]="description"
          [fileName]="fileName"
          [chatHistory]="chatHistory"
          [baseContent]="requirementForm.getRawValue().content"
          (getContent)="appendRequirement($event)"
          (updateChatHistory)="updateChatHistory($event)"
          [containerClass]="'border-none h-full'"
          [brds]="getBRDDataFromIds(requirementForm.getRawValue().linkedBRDIds)"
        />
      </div>
    </div>

    <!-- for non prd requirements -->
    <div
      *ngIf="mode === 'edit' && !isPRD()"
      class="flex flex-col bg-white border rounded lg:col-span-1 h-[calc(100vh-80px)]"
    >
      <app-chat
        class="h-full block"
        chatType="requirement"
        [name]="name"
        [description]="description"
        [fileName]="fileName"
        [chatHistory]="chatHistory"
        [baseContent]="requirementForm.getRawValue().content"
        (getContent)="appendRequirement($event)"
        (updateChatHistory)="updateChatHistory($event)"
        [containerClass]="'border-none h-full'"
      />
    </div>
  </div>
</div>
