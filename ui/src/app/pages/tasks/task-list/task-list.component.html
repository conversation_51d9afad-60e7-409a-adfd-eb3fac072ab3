<div class="max-w-6xl mx-auto">
  <app-workflow-progress-dialog
    [isVisible]="showProgressDialog"
    [projectId]="config.projectId"
    [workflowType]="WorkflowType.Task"
    [isCompleted]="taskGenerationComplete"
    initialTitle="Generating Tasks..."
    completedTitle="Tasks Generated Successfully!"
    subtitle="Creating tasks for the user story..."
    completionButtonText="View Generated Tasks"
    [showCancelButton]="false"
    (closeDialog)="closeProgressDialog()"
  />

  <div
    class="grid grid-cols-12 gap-4"
    *ngIf="selectedUserStory$ | async as selectedUserStory"
  >
    <div
      class="col-span-4 p-4 bg-white rounded-lg border flex flex-col gap-6 h-[calc(100vh-80px)] overflow-auto"
    >
      <h1 class="text-lg font-semibold">
        {{ selectedUserStory.id }}:
        {{ selectedUserStory?.name }}
      </h1>
      <ng-container
        *ngIf="
          formatUserStoryDescriptionForView(
            selectedUserStory?.description
          ) as formattedStoryDescription
        "
      >
        <app-rich-text-editor
          [content]="formattedStoryDescription"
          [editable]="false"
          mode="view"
          editorClass="prose-secondary-edit text-wrap"
        ></app-rich-text-editor>
      </ng-container>
    </div>
    <div
      class="col-span-8 px-4 pb-4 bg-white rounded-lg border h-[calc(100vh-80px)] overflow-auto"
    >
      <div class="py-4 sticky top-0 z-10 bg-white">
        <div class="flex items-center justify-between gap-4">
          <div class="flex items-center">
            <h2 class="text-md font-semibold text-secondary-600">Tasks</h2>
            <app-badge
              [badgeText]="(taskList$ | async)?.length || 0"
            ></app-badge>
          </div>

          <div class="flex justify-between space-x-4">
            <ng-container *ngIf="taskList$ | async as taskList">
              <app-button
                [buttonContent]="
                  taskList.length > 0 ? 'Regenerate Tasks' : 'Generate Tasks'
                "
                theme="secondary"
                size="sm"
                rounded="lg"
                (click)="addExtraContext(taskList.length > 0)"
              />
            </ng-container>

            <app-button
              buttonContent="Add New"
              theme="primary"
              size="sm"
              rounded="lg"
              (click)="navigateToAddTask()"
            />
          </div>
        </div>

        <app-search-input
          *ngIf="taskList$ | async as taskList"
          placeholder="Search..."
          (searchChange)="onSearch($event)"
        ></app-search-input>
      </div>

      <ng-container
        class="overflow-y-auto"
        *ngIf="filteredTaskList$ | async as taskList"
      >
        <app-list-item
          [payload]="{
            description: task.formattedAcceptance ?? '',
            name: task.list,
            id: task.id,
            pmoId: task.pmoId,
            metadata: metadata,
          }"
          [tag]="task.id"
          *ngFor="let task of taskList"
          (click)="navigateToEditTask(task.id, selectedUserStory?.id)"
        >
          <div class="absolute top-4 right-4">
            <app-button
              theme="secondary_outline"
              size="xs"
              rounded="lg"
              [isIconButton]="true"
              icon="heroDocumentDuplicate"
              (click)="copyTaskContent($event, task)"
              matTooltip="Copy"
            />
          </div>
        </app-list-item>
        <div
          class="flex items-center justify-center"
          *ngIf="taskList.length === 0"
        >
          <h1 class="font-semibold w-full text-center mt-3">
            No Tasks Available.
          </h1>
        </div>
      </ng-container>
    </div>
  </div>
</div>
