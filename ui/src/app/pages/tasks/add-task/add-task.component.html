<div class="mx-auto max-w-6xl h-full">
  <div
    [ngClass]="
      mode === 'add'
        ? 'grid grid-cols-1 gap-4'
        : 'grid grid-cols-1 lg:grid-cols-3 gap-4'
    "
  >
    <div
      [ngClass]="
        mode === 'add'
          ? 'bg-white border border-secondary-200 rounded-lg flex flex-col h-[calc(100vh-80px)]'
          : 'bg-white border border-secondary-200 rounded-lg flex flex-col lg:col-span-2 h-[calc(100vh-80px)]'
      "
    >
      <header
        class="flex justify-between items-center p-4 border-b border-secondary-200"
      >
        <h1 class="text-normal font-semibold">
          {{ mode === "add" ? "Add Task" : existingTask.id }}
        </h1>

        <div *ngIf="mode === 'edit'">
          <app-button
            [isIconButton]="true"
            icon="heroTrash"
            theme="danger"
            size="sm"
            rounded="md"
            (click)="deleteTask()"
            matTooltip="Delete"
          ></app-button>
        </div>
      </header>

      <form
        [formGroup]="taskForm"
        (ngSubmit)="mode === 'add' ? addTask() : updateTask()"
        class="flex flex-col h-full overflow-hidden"
      >
        <div class="p-4 flex flex-col gap-1.5 flex-1 overflow-y-auto">
          <div class="flex flex-col">
            <app-input-field
              [required]="true"
              elementPlaceHolder="Task Name"
              elementId="add-task-name"
              elementName="Task Name"
              formControlName="list"
            />
            <ng-container *ngIf="taskForm.get('list')?.touched">
              <span
                *ngIf="taskForm.get('list')?.errors?.['required']"
                class="text-red-500 text-sm"
              >
                Task Name is required
              </span>
            </ng-container>
          </div>
          <div class="flex flex-col gap-1.5">
            <label
              for="elementId"
              class="block text-sm font-medium text-secondary-500"
            >
              Task Description
              <span class="text-red-500 text-xs">*</span>
            </label>
            <div class="flex flex-col gap-1.5">
              <app-rich-text-editor
                #editor
                formControlName="acceptance"
                editorClass="h-[calc(100vh-460px)] min-h-24 overflow-y-auto"
                [editable]="mode === 'edit'"
                appInlineEdit
                [editorInstance]="editor?.editor"
                [contextProvider]="getContentContext.bind(this)"
                [onContentUpdated]="handleInlineEditUpdate.bind(this)"
                (change)="onEditorReady(editor)"
              ></app-rich-text-editor>
              <ng-container *ngIf="taskForm.get('acceptance')?.touched">
                <span
                  *ngIf="taskForm.get('acceptance')?.errors?.['required']"
                  class="text-red-500 text-sm"
                >
                  Task Description is required
                </span>
              </ng-container>
            </div>
          </div>
          <app-multi-upload
            *ngIf="mode === 'add'"
            (fileContent)="handleFileContent($event)"
          />
        </div>

        <div class="flex justify-end gap-2 p-4 border-t border-secondary-200">
          <app-button
            buttonContent="Enhance with AI"
            icon="heroSparklesSolid"
            theme="secondary_outline"
            size="sm"
            rounded="lg"
            (click)="enhanceTaskWithAI()"
            [disabled]="taskForm.invalid"
          ></app-button>
          <app-button
            buttonContent="Update"
            theme="primary"
            size="sm"
            rounded="md"
            *ngIf="mode === 'edit'"
            type="submit"
            [disabled]="taskForm.invalid"
          />
          <app-button
            buttonContent="Add"
            theme="primary"
            size="sm"
            rounded="md"
            *ngIf="mode === 'add'"
            type="submit"
            [disabled]="taskForm.invalid"
          />
        </div>
      </form>
    </div>
    <div *ngIf="mode !== 'add'" class="lg:col-span-1 h-[calc(100vh-80px)]">
      <app-chat
        chatType="task"
        [name]="projectMetadata?.name"
        [description]="projectMetadata?.description"
        [chatHistory]="chatHistory"
        [baseContent]="taskForm.getRawValue().acceptance"
        [prd]="prd.requirement"
        [userStory]="userStory.description"
        (getContent)="updateTaskFromChat($event)"
        (updateChatHistory)="updateChatHistory($event)"
      ></app-chat>
    </div>
  </div>
</div>
