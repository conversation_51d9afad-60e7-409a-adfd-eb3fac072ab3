export const JIRA_TOAST = {
  SUCCESS: 'JIRA Sync Successful',
  ERROR: '<PERSON>IRA Sync Failed',
  INFO: 'JIRA Sync Initiated',
};

export const ADO_TOAST = {
  PULL_INITIATED: 'ADO Pull Operation Started',
  PUSH_INITIATED: 'ADO Push Operation Started',
};

export const APP_INTEGRATIONS = {
  JIRA: {
    SUCCESS: 'Successfully Authenticated with <PERSON>IR<PERSON>',
    DISCONNECT: 'Successfully Disconnected from JIRA',
    ERROR: 'Failed to Authenticate with <PERSON>IR<PERSON>',
  },
  ADO: {
    SUCCESS: 'Azure DevOps connected successfully',
    DISCONNECT: 'Azure DevOps disconnected successfully',
    ERROR: 'Failed to connect to Azure DevOps',
    VALIDATION_ERROR:
      'Invalid Azure DevOps credentials, please check and try again',
  },
  BEDROCK: {
    SUCCESS: 'AWS Bedrock Knowledge Base Config Updated',
    DISCONNECT: 'Successfully Disconnected from AWS Bedrock Knowledge Base',
    INVALID: 'Invalid AWS Knowledge Base Id please check the id and try again',
    ERROR: 'Failed to Update AWS Bedrock Knowledge Base Config',
  },
};

export const DEFAULT_TOAST_DURATION = 5000;
