////Temporary fix for tooltip issue - Need to consider later
@import "~@angular/material/prebuilt-themes/indigo-pink.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .prose-xs {
    @apply text-xs;
  }
}

// Import Font
@font-face {
  font-family: Geist;
  src: url(assets/fonts/geist-variable.woff2) format("woff2");
  font-display: swap;
  font-weight: 100 900;
}

// Color of overlay background
$color-overlay: rgba(0, 0, 0, 0.5) !default;

// Position of dialog
$dialog-position-top: 1.75rem !default;
$dialog-position-bottom: 1.75rem !default;
$dialog-position-left: 1.75rem !default;
$dialog-position-right: 1.75rem !default;

// Transition time
// !! The same as the hideDelay variable defined in ngx-smart-modal.component.ts
$transition-duration: 500ms !default;

// Transition effect
// linear | ease | ease-in | ease-out | ease-in-out
$transition-timing-function: ease-in-out !default;

// Body overflow when a modal is opened.
// Set it to `auto` if you want to unlock the page scroll when a modal is opened
$opened-modal-body-overflow: hidden !default;

// Body if modal is opened
body.dialog-open {
  overflow: $opened-modal-body-overflow;
}

// Close button in modal
.nsm-dialog-btn-close {
  border: 0;
  background: none;
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 1.2em;
  cursor: pointer;
  margin: 10px;
}

// Overlay
.overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow-x: hidden;
  overflow-y: auto;
  transition: background-color $transition-duration;
  background-color: transparent;
  z-index: 999;

  &.nsm-overlay-open {
    background-color: $color-overlay;
  }

  &.transparent {
    background-color: transparent;
  }
}

// Dialog modal
.nsm-dialog {
  position: relative;
  opacity: 1;
  visibility: visible;
  min-height: 200px;
  width: 100%;
  max-width: 520px;
  margin: 0 auto;
  pointer-events: none;
  outline: none;

  // When dialog is closing
  &.nsm-dialog-close {
    opacity: 0;
  }

  &.nsm-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 1.75rem * 2);
  }
}

.nsm-content {
  position: relative;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
  background-clip: padding-box;
  background-color: #fff;
  border-radius: 2px;
  padding: 1rem;
  margin-top: $dialog-position-top;
  margin-bottom: $dialog-position-bottom;
  margin-left: $dialog-position-left;
  margin-right: $dialog-position-right;
  box-shadow:
    0 7px 8px -4px rgba(0, 0, 0, 0.2),
    0 13px 19px 2px rgba(0, 0, 0, 0.14),
    0 5px 24px 4px rgba(0, 0, 0, 0.12);
  outline: 0;

  // For performance purpose
  transform: translate3d(0, 0, 0);
}

.nsm-body {
  position: relative;
  flex: 1 1 auto;
}

/* *************************
 * Animations
 * *************************/

.nsm-dialog[class*="nsm-dialog-animation-"] {
  transition:
    transform $transition-duration $transition-timing-function,
    opacity $transition-duration;
}

// Left to right (ltr)
.nsm-dialog-animation-ltr {
  transform: translate3d(-50%, 0, 0);

  &.nsm-dialog-open {
    transform: translate3d(0, 0, 0);
  }

  &.nsm-dialog-close {
    transform: translate3d(-50%, 0, 0);
  }
}

// Right to left (ltr)
.nsm-dialog-animation-rtl {
  transform: translate3d(50%, 0, 0);

  &.nsm-dialog-open {
    transform: translate3d(0, 0, 0);
  }

  &.nsm-dialog-close {
    transform: translate3d(50%, 0, 0);
  }
}

// Top to bottom (ttb)
.nsm-dialog-animation-ttb {
  transform: translate3d(0, -50%, 0);

  &.nsm-dialog-open {
    transform: translate3d(0, 0, 0);
  }

  &.nsm-dialog-close {
    transform: translate3d(0, -50%, 0);
  }
}

// Bottom to top (btt)
.nsm-dialog-animation-btt {
  transform: translate3d(0, 50%, 0);

  &.nsm-dialog-open {
    transform: translate3d(0, 0, 0);
  }

  &.nsm-dialog-close {
    transform: translate3d(0, 50%, 0);
  }
}

.backdrop {
  position: fixed !important;
}

//Temporary fix for snackbar - Need to consider later
.cdk-overlay-container {
  z-index: 999;
}

.cdk-overlay-container .cdk-global-overlay-wrapper {
  overflow: auto;
}

// svg#mermaid {
//   max-width: 800px !important;
//   max-height: 450px !important;
// }

ngx-loading {
  .backdrop {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .spinner-three-bounce {
    position: fixed !important;
    top: 50%;
    left: 50%;
    inset: none !important;
    transform: translate(-50%, -50%);
    z-index: 2000;
  }
}

html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.chat-history {
  min-height: 510px;
  height: calc(100dvh - 282px);

  .chat-log, .suggestion {
    &:first-child {
      margin-top: auto;
    }
  }
}

.alter-height {
  .chat-history {
    min-height: 650px;
    height: calc(100dvh - 324px);
  }
}

.mat-mdc-menu-content {
  padding: 0 !important;
}

* {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    transition: background-color 0.2s ease;
    
    &:hover {
      background: rgba(0, 0, 0, 0.4);
    }
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }

  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}
