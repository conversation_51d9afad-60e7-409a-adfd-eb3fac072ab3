{"name": "ui", "version": "2.7.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "build:prod": "ng build --configuration production", "build:qa": "ng build --configuration qa", "build:dev": "ng build --configuration dev --base-href=./", "build:uat": "ng build --configuration uat", "prepare": "husky", "watch:dev": "ng build --configuration dev --watch --base-href=./"}, "engines": {"node": ">=18.17"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/material": "^16.2.12", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@atlaskit/editor-json-transformer": "^8.24.0", "@atlaskit/editor-markdown-transformer": "^5.16.0", "@electron/remote": "^2.1.2", "@ng-icons/core": "^28.1.0", "@ng-icons/heroicons": "^30.1.0", "@ngxs/devtools-plugin": "18.0.0", "@ngxs/form-plugin": "18.0.0", "@ngxs/hmr-plugin": "18.0.0", "@ngxs/logger-plugin": "18.0.0", "@ngxs/router-plugin": "18.0.0", "@ngxs/storage-plugin": "18.0.0", "@ngxs/store": "^18.0.0", "@sentry/angular": "^8.26.0", "@sentry/browser": "^8.26.0", "@sentry/cli": "^2.33.1", "@sentry/electron": "^5.3.0", "@sentry/node": "^8.26.0", "@sentry/replay": "^7.116.0", "@sentry/tracing": "^7.114.0", "@tiptap/core": "^2.11.5", "@tiptap/extension-bold": "^2.11.5", "@tiptap/extension-bullet-list": "^2.11.5", "@tiptap/extension-document": "^2.11.5", "@tiptap/extension-gapcursor": "^2.14.0", "@tiptap/extension-hard-break": "^2.11.5", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-history": "^2.11.5", "@tiptap/extension-horizontal-rule": "^2.11.5", "@tiptap/extension-italic": "^2.11.5", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-list-item": "^2.11.5", "@tiptap/extension-list-keymap": "^2.11.5", "@tiptap/extension-ordered-list": "^2.11.5", "@tiptap/extension-paragraph": "^2.11.5", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-text": "^2.11.5", "@tiptap/pm": "^2.11.5", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "markdown-it": "^14.1.0", "markdown-truncate": "^1.1.1", "mermaid": "^10.9.1", "net": "^1.0.2", "ng-angular-popup": "^0.6.1", "ng-toasty": "^0.0.7", "ngx-loading": "^16.0.0", "ngx-modal-dialog": "^4.0.0", "ngx-smart-modal": "^14.0.3", "npm-run-all": "^4.1.5", "panzoom": "^9.4.3", "posthog-js": "^1.181.0", "rehype-parse": "^9.0.1", "rehype-remark": "^10.0.0", "remark-gfm": "^4.0.1", "remark-stringify": "^11.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "unified": "^11.0.5", "uuid": "^10.0.0", "zod": "^3.24.3", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.1", "@angular/cli": "~16.2.1", "@angular/compiler-cli": "^16.2.0", "@rrweb/types": "^2.0.0-alpha.16", "@tailwindcss/typography": "^0.5.16", "@types/d3": "^7.4.3", "@types/dompurify": "~3.0.5", "@types/file-saver": "^2.0.7", "@types/jasmine": "~4.3.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.19", "concurrently": "^8.2.2", "husky": "^9.0.11", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "lint-staged": "^15.2.7", "ngx-logger": "^5.0.12", "postcss": "^8.4.39", "prettier": "3.3.2", "rrweb-snapshot": "^2.0.0-alpha.16", "tailwindcss": "^3.4.4", "typescript": "~5.1.3"}}