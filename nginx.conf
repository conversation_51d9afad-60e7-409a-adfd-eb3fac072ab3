worker_processes 1;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    server {
        listen 80;
        server_name localhost;

        # location / {
        #     root /usr/share/nginx/html;
        #     try_files $uri $uri/ =404;
        # }

        location /api/ {
            proxy_pass http://localhost:5001/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Serve downloads from the /downloads directory
        # location /downloads/ {
        #     root /usr/share/nginx/html;
        #     autoindex on; # Enables directory listing
        # }

        error_page 404 /index.html; # Fallback for Angular SPA
    }
}


