name: Build Components

on:
  workflow_call:
    inputs:
      version:
        required: true
        type: string
      environment:
        required: true
        type: string
        description: 'Either "development" or "production"'
    secrets:
      AWS_OIDC_ROLE_ARN:
        required: true
      AWS_DEFAULT_REGION:
        required: true
      ELECTRON_S3_BUCKET:
        required: true
      APPLE_CSC_SECRET_NAME:
        required: true
      APPLE_ID:
        required: true
      APPLE_APP_SPECIFIC_PASSWORD:
        required: true
      ENABLE_POSTHOG:
        required: true
      POSTHOG_HOST:
        required: true
      POSTHOG_KEY:
        required: true
      ENABLE_LANGFUSE:
        required: true
      DEV_LANGFUSE_SECRET_KEY:
        required: true
      DEV_LANGFUSE_PUBLIC_KEY:
        required: true
      DEV_LANGFUSE_BASE_URL:
        required: true
      DEV_ENABLE_LANGFUSE_DETAILED_TRACES:
        required: true
      PROD_LANGFUSE_SECRET_KEY:
        required: true
      PROD_LANGFUSE_PUBLIC_KEY:
        required: true
      PROD_LANGFUSE_BASE_URL:
        required: true
      PROD_ENABLE_LANGFUSE_DETAILED_TRACES:
        required: true

jobs:
  build_frontend:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "23"

      - name: Restore Frontend Cache
        uses: actions/cache@v4
        with:
          path: ui/node_modules
          key: ${{ runner.os }}-npm-${{ github.ref_name }}-${{ hashFiles('ui/package-lock.json') }}

      - name: Build Frontend
        working-directory: ui
        run: |
          if [[ "${{ inputs.environment }}" == "development" ]]; then
            npm run build:dev
          else
            npm run build:prod
          fi

      - name: Upload Frontend Build
        uses: actions/upload-artifact@v4
        with:
          name: frontend-build
          path: ui/dist/

  build_electron_windows:
    needs: [build_frontend]
    runs-on: windows-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Get Short Commit SHA
        id: get-sha
        shell: pwsh
        run: |
          $COMMIT_SHA = (git rev-parse --short HEAD) 2>$null
          if (-not $COMMIT_SHA) {
            $COMMIT_SHA = $env:GITHUB_SHA.Substring(0,7)
          }
          echo "COMMIT_SHA=$COMMIT_SHA" >> $env:GITHUB_OUTPUT
          echo "Using short commit SHA: $COMMIT_SHA"

      - name: Configure AWS credentials
        id: aws-credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_OIDC_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}
          role-session-name: GitHubActions-${{ github.event.repository.name }}-${{ github.run_id }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 23

      - name: Download Frontend Build
        uses: actions/download-artifact@v4
        with:
          name: frontend-build
          path: ui/dist/

      - name: Copy Frontend to Electron Directory
        run: |
          cp -r ui/dist/ui electron/

      - name: Install Dependencies and Build Electron
        shell: pwsh
        run: |
          cd electron

          # Append APP_ENVIRONMENT configuration to .env file
          Add-Content -Path .env -Value "APP_ENVIRONMENT=${{ inputs.environment }}"

          # Append to .env file with PostHog configuration
          Add-Content -Path .env -Value "ENABLE_POSTHOG=$env:ENABLE_POSTHOG"
          Add-Content -Path .env -Value "POSTHOG_HOST=$env:POSTHOG_HOST"
          Add-Content -Path .env -Value "POSTHOG_KEY=$env:POSTHOG_KEY"
          Add-Content -Path .env -Value "ENABLE_LANGFUSE=$env:ENABLE_LANGFUSE"
          Add-Content -Path .env -Value "LANGFUSE_SECRET_KEY=$env:LANGFUSE_SECRET_KEY"
          Add-Content -Path .env -Value "LANGFUSE_PUBLIC_KEY=$env:LANGFUSE_PUBLIC_KEY"
          Add-Content -Path .env -Value "LANGFUSE_BASE_URL=$env:LANGFUSE_BASE_URL"
          Add-Content -Path .env -Value "ENABLE_LANGFUSE_DETAILED_TRACES=$env:ENABLE_LANGFUSE_DETAILED_TRACES"

          Write-Host "POSTHOG_HOST is: $env:POSTHOG_HOST"

          npm install
          npm run build
          npm run package:win

          # List what's actually created
          Write-Host "`nListing dist contents:"
          Get-ChildItem -Recurse dist | Format-List
        env:
          ENABLE_POSTHOG: ${{ secrets.ENABLE_POSTHOG }}
          POSTHOG_HOST: ${{ secrets.POSTHOG_HOST }}
          POSTHOG_KEY: ${{ secrets.POSTHOG_KEY }}
          ENABLE_LANGFUSE: ${{ secrets.ENABLE_LANGFUSE }}
          LANGFUSE_SECRET_KEY: ${{ inputs.environment == 'production' && secrets.PROD_LANGFUSE_SECRET_KEY || secrets.DEV_LANGFUSE_SECRET_KEY }}
          LANGFUSE_PUBLIC_KEY: ${{ inputs.environment == 'production' && secrets.PROD_LANGFUSE_PUBLIC_KEY || secrets.DEV_LANGFUSE_PUBLIC_KEY }}
          LANGFUSE_BASE_URL: ${{ inputs.environment == 'production' && secrets.PROD_LANGFUSE_BASE_URL || secrets.DEV_LANGFUSE_BASE_URL }}
          ENABLE_LANGFUSE_DETAILED_TRACES: ${{ inputs.environment == 'production' && secrets.PROD_ENABLE_LANGFUSE_DETAILED_TRACES || secrets.DEV_ENABLE_LANGFUSE_DETAILED_TRACES }}

      - name: Package Windows Build
        run: |
          cd electron
          $VERSION = (node -e "console.log(require('./package.json').version);" | Out-String).Trim()

          # Create out directory
          New-Item -ItemType Directory -Path out -Force

          # Compress win-unpacked to out folder
          Compress-Archive -Path dist/win-unpacked/* -DestinationPath out/win-unpacked.zip

          # Copy necessary files to out folder
          Copy-Item dist/latest.yml out/
          Copy-Item dist/*Setup*.exe out/ -ErrorAction SilentlyContinue
          Copy-Item dist/*Setup*.exe.blockmap out/ -ErrorAction SilentlyContinue

          # Upload to S3 for long-term storage using sync
          aws s3 sync out/ "s3://${{ secrets.ELECTRON_S3_BUCKET }}/github-actions/${{ inputs.environment }}/windows/$VERSION/${{steps.get-sha.outputs.COMMIT_SHA}}/"

      - name: Upload Windows Build as GitHub Artifact
        uses: actions/upload-artifact@v4
        with:
          name: windows-electron-build
          path: |
            electron/out/win-unpacked.zip
            electron/out/latest.yml
            electron/out/*Setup*.exe
            electron/out/*Setup*.exe.blockmap
          retention-days: 90

  build_electron_mac:
    needs: [build_frontend]
    runs-on: macos-15
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Get Short Commit SHA
        id: get-sha
        run: |
          COMMIT_SHA=$(git rev-parse --short HEAD || echo "${GITHUB_SHA:0:7}")
          echo "COMMIT_SHA=${COMMIT_SHA}" >> $GITHUB_OUTPUT
          echo "Using short commit SHA: ${COMMIT_SHA}"

      - name: Configure AWS credentials
        id: aws-credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_OIDC_ROLE_ARN }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}
          role-session-name: GitHubActions-${{ github.event.repository.name }}-${{ github.run_id }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 23

      - name: Download Frontend Build
        uses: actions/download-artifact@v4
        with:
          name: frontend-build
          path: ui/dist/

      - name: Fetch Apple Developer Certificate
        run: |
          CSC_DATA=$(aws secretsmanager get-secret-value --secret-id ${{ secrets.APPLE_CSC_SECRET_NAME }} --query SecretString --output text)
          echo "::add-mask::$CSC_DATA"
          # Extract credentials and mask them
          CSC_LINK=$(echo $CSC_DATA | jq -r '.CSC_LINK')
          CSC_KEY_PASSWORD=$(echo $CSC_DATA | jq -r '.CSC_KEY_PASSWORD')

          # Mask sensitive values before setting them as environment variables
          echo "::add-mask::$CSC_LINK"
          echo "::add-mask::$CSC_KEY_PASSWORD"

          # Set the masked values as environment variables
          echo "CSC_LINK=$CSC_LINK" >> $GITHUB_ENV
          echo "CSC_KEY_PASSWORD=$CSC_KEY_PASSWORD" >> $GITHUB_ENV

      - name: Copy Frontend to Electron Directory
        run: |
          cp -r ui/dist/ui electron/

      - name: Install Dependencies and Build Electron
        run: |
          cd electron

          # Create .env file with APP_ENVIRONMENT configuration
          echo "APP_ENVIRONMENT=${{ inputs.environment }}" >> .env

          # Create .env file with PostHog configuration
          echo "ENABLE_POSTHOG=$ENABLE_POSTHOG" >> .env
          echo "POSTHOG_HOST=$POSTHOG_HOST" >> .env
          echo "POSTHOG_KEY=$POSTHOG_KEY" >> .env
          echo "ENABLE_LANGFUSE=$ENABLE_LANGFUSE" >> .env
          echo "LANGFUSE_SECRET_KEY=$LANGFUSE_SECRET_KEY" >> .env
          echo "LANGFUSE_PUBLIC_KEY=$LANGFUSE_PUBLIC_KEY" >> .env
          echo "LANGFUSE_BASE_URL=$LANGFUSE_BASE_URL" >> .env
          echo "ENABLE_LANGFUSE_DETAILED_TRACES=$ENABLE_LANGFUSE_DETAILED_TRACES" >> .env

          npm install
          npm run build
          npm run package:mac
        env:
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_APP_SPECIFIC_PASSWORD: ${{ secrets.APPLE_APP_SPECIFIC_PASSWORD }}
          ENABLE_POSTHOG: ${{ secrets.ENABLE_POSTHOG }}
          POSTHOG_HOST: ${{ secrets.POSTHOG_HOST }}
          POSTHOG_KEY: ${{ secrets.POSTHOG_KEY }}
          ENABLE_LANGFUSE: ${{ secrets.ENABLE_LANGFUSE }}
          LANGFUSE_SECRET_KEY: ${{ inputs.environment == 'production' && secrets.PROD_LANGFUSE_SECRET_KEY || secrets.DEV_LANGFUSE_SECRET_KEY }}
          LANGFUSE_PUBLIC_KEY: ${{ inputs.environment == 'production' && secrets.PROD_LANGFUSE_PUBLIC_KEY || secrets.DEV_LANGFUSE_PUBLIC_KEY }}
          LANGFUSE_BASE_URL: ${{ inputs.environment == 'production' && secrets.PROD_LANGFUSE_BASE_URL || secrets.DEV_LANGFUSE_BASE_URL }}
          ENABLE_LANGFUSE_DETAILED_TRACES: ${{ inputs.environment == 'production' && secrets.PROD_ENABLE_LANGFUSE_DETAILED_TRACES || secrets.DEV_ENABLE_LANGFUSE_DETAILED_TRACES }}

      - name: Package and Upload to S3
        id: package_and_upload
        run: |
          cd electron
          VERSION=$(node -e "console.log(require('./package.json').version);")
          echo "VERSION=$VERSION" >> $GITHUB_ENV

          # Upload to S3 for long-term storage
          aws s3 sync dist/ "s3://${{ secrets.ELECTRON_S3_BUCKET }}/github-actions/${{ inputs.environment }}/darwin/$VERSION/${{steps.get-sha.outputs.COMMIT_SHA}}/" --exclude "dist/mac/*" --exclude "dist/mac-arm64/*"

      - name: Upload macOS Intel Build as GitHub Artifact
        uses: actions/upload-artifact@v4
        with:
          name: macos-intel-build
          path: |
            electron/dist/*-${{ env.VERSION }}.dmg
            electron/dist/*-${{ env.VERSION }}.dmg.blockmap
            electron/dist/*-${{ env.VERSION }}-mac.zip
            electron/dist/*-${{ env.VERSION }}-mac.zip.blockmap
            electron/dist/latest-mac.yml
          retention-days: 90

      - name: Upload macOS ARM64 Build as GitHub Artifact
        uses: actions/upload-artifact@v4
        with:
          name: macos-arm64-build
          path: |
            electron/dist/*-${{ env.VERSION }}-arm64.dmg
            electron/dist/*-${{ env.VERSION }}-arm64.dmg.blockmap
            electron/dist/*-${{ env.VERSION }}-arm64-mac.zip
            electron/dist/*-${{ env.VERSION }}-arm64-mac.zip.blockmap
            electron/dist/latest-mac.yml
          retention-days: 90
