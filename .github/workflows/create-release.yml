name: Create GitHub Release

on:
  workflow_call:
    inputs:
      version:
        required: true
        type: string

jobs:
  create_release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Download Windows Artifact
        uses: actions/download-artifact@v4
        with:
          name: windows-electron-build
          path: release-assets/windows

      - name: Download macOS Intel Artifact
        uses: actions/download-artifact@v4
        with:
          name: macos-intel-build
          path: release-assets/macos-intel

      - name: Download macOS ARM64 Artifact
        uses: actions/download-artifact@v4
        with:
          name: macos-arm64-build
          path: release-assets/macos-arm64

      - name: Rename files with consistent naming convention
        run: |
          VERSION="${{ inputs.version }}"
          APP_NAME="Specif-AI"
          APP_SETUP_NAME="${APP_NAME}-Setup"
          echo "Creating consistently named assets for version $VERSION"

          # Create directory for renamed files
          mkdir -p renamed-assets

          # Rename Windows artifact
          WIN_ZIP=$(find release-assets/windows -name "*.zip" -type f | head -1)
          if [[ -n "$WIN_ZIP" ]]; then
            cp "$WIN_ZIP" "renamed-assets/${APP_NAME}-${VERSION}-windows.zip"
            echo "✅ Created Windows artifact: ${APP_NAME}-${VERSION}-windows.zip"
          else
            echo "❌ No Windows zip file found"
          fi

          # Move Windows exe file
          WIN_EXE=$(find release-assets/windows -name "*Setup*.exe" -type f | head -1)
          if [[ -n "$WIN_EXE" ]]; then
            cp "$WIN_EXE" "renamed-assets/${APP_SETUP_NAME}-${VERSION}.exe"
            echo "✅ Created Windows installer: ${APP_SETUP_NAME}-${VERSION}.exe"
          else
            echo "❌ No Windows installer found"
          fi

          # Move Windows blockmap file
          WIN_BLOCKMAP=$(find release-assets/windows -name "*Setup*.exe.blockmap" -type f | head -1)
          if [[ -n "$WIN_BLOCKMAP" ]]; then
            cp "$WIN_BLOCKMAP" "renamed-assets/${APP_SETUP_NAME}-${VERSION}.exe.blockmap"
            echo "✅ Created Windows blockmap: ${APP_SETUP_NAME}-${VERSION}.exe.blockmap"
          else
            echo "❌ No Windows blockmap found"
          fi

          # Move windows yml files
          cp release-assets/windows/latest.yml renamed-assets/latest.yml

          # Rename macOS artifact
          shopt -s nullglob

          for dir in release-assets/macos-intel release-assets/macos-arm64; do
              for file in "$dir"/Specif\ AI-*; do
                  new_name="renamed-assets/$(basename "$file" | sed "s/Specif AI/${APP_NAME}/g")"
                  mv "$file" "$new_name"
                  echo "Moved and Renamed: $file → $new_name"
              done
          done

          # Move macos yml files
          cp release-assets/macos-arm64/latest-mac.yml renamed-assets/latest-mac.yml

          echo "📦 Final release assets:"
          ls -la renamed-assets/

      - name: Modify Changelog
        run: |
          VERSION="${{ inputs.version }}"
          CHANGELOG_CONTENT=$(sed -n "/## \[$VERSION\]/,/## \[/p" CHANGELOG.md | sed '$d')
          echo "$CHANGELOG_CONTENT" > CHANGELOG-MOD.md

      - name: Create Release with Assets
        uses: softprops/action-gh-release@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tag_name: v${{ inputs.version }}
          name: Release v${{ inputs.version }}
          draft: true
          files: |
            renamed-assets/*
          body_path: CHANGELOG-MOD.md
          fail_on_unmatched_files: false
