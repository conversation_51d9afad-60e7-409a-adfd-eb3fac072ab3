name: Sync GitHub to Git<PERSON>ab

on:
  push:
    branches:
      - main

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
      # Step 1: Checkout GitHub repository with full history
      - name: Checkout GitHub Repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 0  # Fetch full history for context

      # Step 2: Configure Git identity
      - name: Configure Git Identity
        run: |
          git config --global user.name "GitHub Actions Bot"
          git config --global user.email "keluma<PERSON>@presidio.com"

      # Step 3: Setup SSH for GitLab
      - name: Setup SSH for GitLab
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.GITLAB_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H gitlab.presidio.com >> ~/.ssh/known_hosts

      # Step 4: Add GitLab remote 
      - name: Add GitLab remote
        run: git remote <NAME_EMAIL>:hai-build/specif-ai-github.git

      # Step 5: Fetch GitLab release/QA branch
      - name: Fetch GitLab release/QA branch
        run: git fetch gitlab release/QA || true  # Ensure GitLab release/QA branch is fetched

      # Step 6: Checkout GitLab release/QA branch
      - name: Checkout GitLab release/QA branch
        run: |
          git checkout release/QA || git checkout -b release/QA gitlab/release/QA

      # Step 7: Merge GitHub main into GitLab release/QA
      - name: Merge GitHub main into GitLab release/QA
        run: git merge origin/main --allow-unrelated-histories --strategy-option=theirs || true

      # Step 8: Push only to GitLab release/QA
      - name: Push Final Changes to GitLab release/QA branch
        run: git push gitlab release/QA