name: Deploy Docs to GitHub Pages

on:
  push:
    branches:
      - main
    paths:
      - "docs/**"

permissions:
  contents: write

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 22

      - name: Install dependencies
        run: |
          cd docs
          npm install

      - name: Build documentation
        run: |
          cd docs
          npm run build

      - name: Deploy to GitHub Pages
        if: ${{ github.event_name == 'push' }}
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: docs/build
          branch: gh-pages
